<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=5.0, user-scalable=yes" />
  <meta name="mobile-web-app-capable" content="yes" />
  <meta name="apple-mobile-web-app-capable" content="yes" />
  <meta name="apple-mobile-web-app-status-bar-style" content="black-translucent" />
  <meta name="theme-color" content="#2d5016" />
  <meta name="description" content="Luxury 4 BHK Row Villas in Pimple Nilakh – Green Alley offers nature-facing villas with premium amenities and smart living." />
  <title>Green Alley | Luxury Villas in Pimple Nilakh</title>

  <!-- Fonts -->
  <link rel="preconnect" href="https://fonts.googleapis.com">
  <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
  <link href="https://fonts.googleapis.com/css2?family=Playfair+Display:wght@400;500;600;700;800;900&family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">

  <!-- CSS Libraries -->
  <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
  <link rel="stylesheet" href="https://unpkg.com/swiper@8/swiper-bundle.min.css" />
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/aos/2.3.4/aos.css" />
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" />

  <!-- Custom Styles -->
  <style>
    :root {
      --primary-green: #2d5016;
      --primary-brown: #8B4513;
      --primary-dark: #1a1a1a;
      --accent-green: #4a7c59;
      --accent-brown: #A0522D;
      --light-green: #6b8e23;
      --dark-brown: #654321;
      --luxury-gradient: linear-gradient(135deg, #2d5016 0%, #4a7c59 50%, #8B4513 100%);
      --green-gradient: linear-gradient(135deg, #2d5016 0%, #6b8e23 50%, #4a7c59 100%);
      --brown-gradient: linear-gradient(135deg, #8B4513 0%, #A0522D 50%, #654321 100%);
      --dark-gradient: linear-gradient(135deg, #1a1a1a 0%, #2d2d2d 100%);
    }

    * {
      scroll-behavior: smooth;
    }

    body {
      font-family: 'Inter', sans-serif;
      overflow-x: hidden;
    }

    .font-playfair {
      font-family: 'Playfair Display', serif;
    }

    .luxury-gradient {
      background: var(--luxury-gradient);
    }

    .green-gradient {
      background: var(--green-gradient);
    }

    .brown-gradient {
      background: var(--brown-gradient);
    }

    .dark-gradient {
      background: var(--dark-gradient);
    }

    /* Fix for gradient text */
    .luxury-gradient.bg-clip-text {
      background: var(--luxury-gradient);
      -webkit-background-clip: text;
      background-clip: text;
      -webkit-text-fill-color: transparent;
      color: transparent;
    }

    /* Gradient text class */
    .gradient-text {
      background: var(--luxury-gradient);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
      background-clip: text;
      color: transparent;
      display: inline-block;
      font-weight: bold;
      background-size: 100%;
      background-repeat: no-repeat;
    }

    /* Enhanced fallback for browsers that don't support background-clip */
    @supports not (-webkit-background-clip: text) {
      .gradient-text {
        color: var(--primary-green) !important;
        background: none !important;
        text-shadow: 0 0 10px rgba(45, 80, 22, 0.5);
      }
    }

    /* Firefox specific fix */
    @-moz-document url-prefix() {
      .gradient-text {
        color: var(--primary-green);
        background: none;
        text-shadow: 0 0 10px rgba(45, 80, 22, 0.5);
      }
    }

    .glass-effect {
      background: rgba(45, 80, 22, 0.1);
      backdrop-filter: blur(10px);
      border: 1px solid rgba(45, 80, 22, 0.2);
    }

    .navbar-glass {
      background: rgba(26, 26, 26, 0.95);
      backdrop-filter: blur(20px);
      border-bottom: 1px solid rgba(45, 80, 22, 0.2);
      box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
    }

    .luxury-shadow {
      box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25), 0 0 0 1px rgba(255, 255, 255, 0.05);
    }

    .hover-lift {
      transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
      position: relative;
      overflow: hidden;
    }

    .hover-lift:hover {
      transform: translateY(-10px) scale(1.02);
      box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
    }

    .hover-lift::before {
      content: '';
      position: absolute;
      top: 0;
      left: -100%;
      width: 100%;
      height: 100%;
      background: linear-gradient(90deg, transparent, rgba(255,255,255,0.1), transparent);
      transition: left 0.6s ease;
      z-index: 1;
    }

    .hover-lift:hover::before {
      left: 100%;
    }

    .parallax {
      transform: translateZ(0);
      will-change: transform;
    }

    /* Advanced Animations */
    @keyframes float {
      0%, 100% { transform: translateY(0px); }
      50% { transform: translateY(-20px); }
    }

    @keyframes glow {
      0%, 100% { box-shadow: 0 0 20px rgba(45, 80, 22, 0.3); }
      50% { box-shadow: 0 0 40px rgba(45, 80, 22, 0.6); }
    }

    @keyframes slideInLeft {
      0% { transform: translateX(-100px); opacity: 0; }
      100% { transform: translateX(0); opacity: 1; }
    }

    @keyframes slideInRight {
      0% { transform: translateX(100px); opacity: 0; }
      100% { transform: translateX(0); opacity: 1; }
    }

    @keyframes scaleIn {
      0% { transform: scale(0.8); opacity: 0; }
      100% { transform: scale(1); opacity: 1; }
    }

    @keyframes rotateIn {
      0% { transform: rotate(-180deg) scale(0.5); opacity: 0; }
      100% { transform: rotate(0deg) scale(1); opacity: 1; }
    }

    @keyframes shimmer {
      0% { background-position: -200% 0; }
      100% { background-position: 200% 0; }
    }

    .floating {
      animation: float 6s ease-in-out infinite;
    }

    .glow-effect {
      animation: glow 3s ease-in-out infinite;
    }

    .slide-in-left {
      animation: slideInLeft 0.8s ease-out;
    }

    .slide-in-right {
      animation: slideInRight 0.8s ease-out;
    }

    .scale-in {
      animation: scaleIn 0.6s ease-out;
    }

    .rotate-in {
      animation: rotateIn 0.8s ease-out;
    }

    .shimmer-effect {
      background: linear-gradient(90deg, transparent, rgba(255,255,255,0.3), transparent);
      background-size: 200% 100%;
      animation: shimmer 2s infinite;
    }

    /* Magnetic Effect */
    .magnetic {
      transition: transform 0.3s ease;
    }

    .magnetic:hover {
      transform: scale(1.05);
    }

    /* Tilt Effect */
    .tilt-effect {
      transition: transform 0.3s ease;
      transform-style: preserve-3d;
    }

    .tilt-effect:hover {
      transform: perspective(1000px) rotateX(5deg) rotateY(5deg);
    }

    /* Morphing Button */
    .morph-btn {
      position: relative;
      overflow: hidden;
      border-radius: 50px;
      transition: all 0.4s ease;
    }

    .morph-btn::before {
      content: '';
      position: absolute;
      top: 50%;
      left: 50%;
      width: 0;
      height: 0;
      background: rgba(255, 255, 255, 0.2);
      border-radius: 50%;
      transform: translate(-50%, -50%);
      transition: width 0.4s ease, height 0.4s ease;
    }

    .morph-btn:hover::before {
      width: 300px;
      height: 300px;
    }

    /* Liquid Animation */
    .liquid-bg {
      position: relative;
      overflow: hidden;
    }

    .liquid-bg::before {
      content: '';
      position: absolute;
      top: -50%;
      left: -50%;
      width: 200%;
      height: 200%;
      background: radial-gradient(circle, rgba(212, 175, 55, 0.1) 0%, transparent 70%);
      animation: float 8s ease-in-out infinite;
      z-index: -1;
    }

    /* Text Reveal Animation */
    .text-reveal {
      overflow: hidden;
      position: relative;
    }

    .text-reveal::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background: linear-gradient(135deg, #D4AF37 0%, #FFD700 50%, #B8860B 100%);
      transform: translateX(-100%);
      animation: reveal 1.5s ease-out forwards;
      animation-delay: 0.5s;
    }

    @keyframes reveal {
      0% { transform: translateX(-100%); }
      50% { transform: translateX(0%); }
      100% { transform: translateX(100%); }
    }

    /* Particle Effect */
    .particle-bg {
      position: relative;
      overflow: hidden;
    }

    .particle {
      position: absolute;
      width: 4px;
      height: 4px;
      background: rgba(212, 175, 55, 0.6);
      border-radius: 50%;
      animation: particle-float 15s infinite linear;
    }

    @keyframes particle-float {
      0% { transform: translateY(100vh) rotate(0deg); opacity: 0; }
      10% { opacity: 1; }
      90% { opacity: 1; }
      100% { transform: translateY(-100vh) rotate(360deg); opacity: 0; }
    }

    .luxury-btn {
      background: var(--luxury-gradient);
      color: white;
      padding: 16px 32px;
      border-radius: 50px;
      font-weight: 600;
      text-transform: uppercase;
      letter-spacing: 1px;
      transition: all 0.3s ease;
      position: relative;
      overflow: hidden;
      border: none;
      cursor: pointer;
      display: inline-flex;
      align-items: center;
      justify-content: center;
      text-decoration: none;
    }

    .luxury-btn::before {
      content: '';
      position: absolute;
      top: 0;
      left: -100%;
      width: 100%;
      height: 100%;
      background: linear-gradient(90deg, transparent, rgba(255,255,255,0.3), transparent);
      transition: left 0.5s;
    }

    .luxury-btn:hover::before {
      left: 100%;
    }

    .luxury-btn:hover {
      transform: translateY(-2px);
      box-shadow: 0 15px 35px rgba(45, 80, 22, 0.4);
      background: var(--green-gradient);
    }

    .floating-whatsapp {
      position: fixed;
      bottom: 30px;
      right: 30px;
      z-index: 1000;
      background: linear-gradient(135deg, #25D366 0%, #128C7E 100%);
      color: white;
      width: 60px;
      height: 60px;
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 24px;
      box-shadow: 0 8px 25px rgba(37, 211, 102, 0.4);
      animation: pulse 2s infinite;
      transition: all 0.3s ease;
      cursor: pointer;
      overflow: hidden;
    }

    .floating-whatsapp::before {
      content: '';
      position: absolute;
      top: 0;
      left: -100%;
      width: 100%;
      height: 100%;
      background: linear-gradient(90deg, transparent, rgba(255,255,255,0.3), transparent);
      transition: left 0.5s;
    }

    .floating-whatsapp:hover::before {
      left: 100%;
    }

    .floating-whatsapp:hover {
      transform: scale(1.15) rotate(5deg);
      box-shadow: 0 15px 40px rgba(37, 211, 102, 0.6);
    }

    .floating-whatsapp:active {
      transform: scale(1.05);
    }

    @keyframes pulse {
      0% { box-shadow: 0 0 0 0 rgba(37, 211, 102, 0.7); }
      70% { box-shadow: 0 0 0 10px rgba(37, 211, 102, 0); }
      100% { box-shadow: 0 0 0 0 rgba(37, 211, 102, 0); }
    }

    @keyframes fadeInUp {
      from {
        opacity: 0;
        transform: translateY(30px);
      }
      to {
        opacity: 1;
        transform: translateY(0);
      }
    }

    .fade-in-up {
      animation: fadeInUp 0.8s ease-out;
    }

    .navbar-glass {
      background: rgba(26, 26, 26, 0.9);
      backdrop-filter: blur(20px);
      border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    }

    .hero-overlay {
      background: linear-gradient(135deg, rgba(26, 26, 26, 0.7) 0%, rgba(45, 80, 22, 0.8) 100%);
    }

    /* Ensure gradient text works in navigation */
    .navbar-glass .gradient-text {
      background: var(--luxury-gradient) !important;
      -webkit-background-clip: text !important;
      -webkit-text-fill-color: transparent !important;
      background-clip: text !important;
      color: transparent !important;
    }

    /* Force gradient text rendering */
    .gradient-text {
      -webkit-font-smoothing: antialiased;
      -moz-osx-font-smoothing: grayscale;
      text-rendering: optimizeLegibility;
    }

    /* Webkit specific gradient text fix */
    @media screen and (-webkit-min-device-pixel-ratio:0) {
      .gradient-text {
        background: var(--luxury-gradient);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
        color: transparent;
      }
    }

    /* Custom Scrollbar */
    ::-webkit-scrollbar {
      width: 14px;
    }

    ::-webkit-scrollbar-track {
      background: linear-gradient(180deg, #1a1a1a 0%, #2d2d2d 50%, #1a1a1a 100%);
      border-radius: 12px;
      box-shadow: inset 0 0 8px rgba(0, 0, 0, 0.4);
      border: 1px solid rgba(255, 255, 255, 0.05);
    }

    ::-webkit-scrollbar-thumb {
      background: var(--luxury-gradient);
      border-radius: 12px;
      box-shadow:
        0 2px 10px rgba(45, 80, 22, 0.4),
        inset 0 1px 0 rgba(255, 255, 255, 0.2),
        inset 0 -1px 0 rgba(0, 0, 0, 0.2);
      border: 2px solid #1a1a1a;
      position: relative;
    }

    ::-webkit-scrollbar-thumb:hover {
      background: var(--green-gradient);
      box-shadow:
        0 4px 20px rgba(45, 80, 22, 0.6),
        inset 0 1px 0 rgba(255, 255, 255, 0.3),
        inset 0 -1px 0 rgba(0, 0, 0, 0.3);
      transform: scaleX(1.1);
    }

    ::-webkit-scrollbar-thumb:active {
      background: var(--brown-gradient);
      box-shadow:
        0 2px 15px rgba(139, 69, 19, 0.8),
        inset 0 2px 4px rgba(0, 0, 0, 0.3);
    }

    ::-webkit-scrollbar-corner {
      background: #1a1a1a;
      border-radius: 12px;
    }

    /* Scrollbar animation */
    ::-webkit-scrollbar-thumb {
      transition: all 0.3s ease;
    }

    /* Custom scrollbar for specific elements */
    .luxury-shadow::-webkit-scrollbar {
      width: 8px;
    }

    .luxury-shadow::-webkit-scrollbar-thumb {
      background: linear-gradient(180deg, rgba(45, 80, 22, 0.8) 0%, rgba(74, 124, 89, 0.6) 100%);
      border-radius: 8px;
    }

    /* Firefox Scrollbar */
    html {
      scrollbar-width: thin;
      scrollbar-color: #D4AF37 #1a1a1a;
    }

    /* Restore Normal Cursor */
    *, *:before, *:after {
      cursor: auto;
    }

    body {
      cursor: auto;
    }

    html {
      cursor: auto;
    }

    /* Enhanced cursor styles for better UX */
    a, button, .luxury-btn, .magnetic, .hover-lift, .nav-link {
      cursor: pointer;
    }

    input, textarea, select {
      cursor: text;
    }

    input[type="submit"], input[type="button"], button {
      cursor: pointer;
    }

    .floating-whatsapp {
      cursor: pointer;
    }

    /* Custom Alert Box */
    .custom-alert-overlay {
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background: rgba(0, 0, 0, 0.7);
      backdrop-filter: blur(10px);
      z-index: 10000;
      display: flex;
      align-items: center;
      justify-content: center;
      opacity: 0;
      visibility: hidden;
      transition: all 0.3s ease;
    }

    .custom-alert-overlay.show {
      opacity: 1;
      visibility: visible;
    }

    .custom-alert-box {
      background: linear-gradient(135deg, #1a1a1a 0%, #2d2d2d 100%);
      border: 2px solid rgba(45, 80, 22, 0.3);
      border-radius: 20px;
      padding: 30px;
      max-width: 500px;
      width: 90%;
      box-shadow:
        0 25px 50px rgba(0, 0, 0, 0.5),
        0 0 0 1px rgba(255, 255, 255, 0.1),
        inset 0 1px 0 rgba(255, 255, 255, 0.1);
      transform: scale(0.8) translateY(50px);
      transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
      position: relative;
      overflow: hidden;
    }

    .custom-alert-overlay.show .custom-alert-box {
      transform: scale(1) translateY(0);
    }

    .custom-alert-box::before {
      content: '';
      position: absolute;
      top: 0;
      left: -100%;
      width: 100%;
      height: 100%;
      background: linear-gradient(90deg, transparent, rgba(45, 80, 22, 0.1), transparent);
      transition: left 2s ease;
    }

    .custom-alert-box.shimmer::before {
      left: 100%;
    }

    .custom-alert-icon {
      width: 60px;
      height: 60px;
      margin: 0 auto 20px;
      background: var(--luxury-gradient);
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 24px;
      color: white;
      box-shadow: 0 10px 25px rgba(45, 80, 22, 0.3);
      animation: pulse-glow 2s infinite;
    }

    @keyframes pulse-glow {
      0%, 100% {
        box-shadow: 0 10px 25px rgba(45, 80, 22, 0.3);
        transform: scale(1);
      }
      50% {
        box-shadow: 0 15px 35px rgba(45, 80, 22, 0.5);
        transform: scale(1.05);
      }
    }

    .custom-alert-title {
      color: white;
      font-size: 24px;
      font-weight: bold;
      text-align: center;
      margin-bottom: 15px;
      font-family: 'Playfair Display', serif;
    }

    .custom-alert-message {
      color: #e5e5e5;
      font-size: 16px;
      line-height: 1.6;
      text-align: center;
      margin-bottom: 25px;
    }

    .custom-alert-buttons {
      display: flex;
      gap: 15px;
      justify-content: center;
    }

    .custom-alert-btn {
      padding: 12px 30px;
      border: none;
      border-radius: 25px;
      font-weight: 600;
      font-size: 14px;
      cursor: none;
      transition: all 0.3s ease;
      position: relative;
      overflow: hidden;
      text-transform: uppercase;
      letter-spacing: 1px;
    }

    .custom-alert-btn-primary {
      background: var(--luxury-gradient);
      color: white;
      box-shadow: 0 5px 15px rgba(45, 80, 22, 0.3);
    }

    .custom-alert-btn-primary:hover {
      transform: translateY(-2px);
      box-shadow: 0 8px 25px rgba(45, 80, 22, 0.4);
    }

    .custom-alert-btn-secondary {
      background: rgba(255, 255, 255, 0.1);
      color: white;
      border: 1px solid rgba(255, 255, 255, 0.2);
    }

    .custom-alert-btn-secondary:hover {
      background: rgba(255, 255, 255, 0.2);
      transform: translateY(-2px);
    }

    .custom-alert-btn::before {
      content: '';
      position: absolute;
      top: 0;
      left: -100%;
      width: 100%;
      height: 100%;
      background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
      transition: left 0.5s;
    }

    .custom-alert-btn:hover::before {
      left: 100%;
    }

    /* Alert types */
    .alert-success .custom-alert-icon {
      background: linear-gradient(135deg, #10B981 0%, #34D399 50%, #059669 100%);
    }

    .alert-error .custom-alert-icon {
      background: linear-gradient(135deg, #EF4444 0%, #F87171 50%, #DC2626 100%);
    }

    .alert-warning .custom-alert-icon {
      background: linear-gradient(135deg, #F59E0B 0%, #FBBF24 50%, #D97706 100%);
    }

    .alert-info .custom-alert-icon {
      background: linear-gradient(135deg, #3B82F6 0%, #60A5FA 50%, #2563EB 100%);
    }

    /* Alert box entrance animations */
    @keyframes alertSlideIn {
      0% {
        transform: scale(0.3) translateY(-100px) rotate(-10deg);
        opacity: 0;
      }
      50% {
        transform: scale(1.05) translateY(10px) rotate(2deg);
        opacity: 0.8;
      }
      100% {
        transform: scale(1) translateY(0) rotate(0deg);
        opacity: 1;
      }
    }

    .custom-alert-box.animate-in {
      animation: alertSlideIn 0.6s cubic-bezier(0.175, 0.885, 0.32, 1.275);
    }

    /* Alert box exit animation */
    @keyframes alertSlideOut {
      0% {
        transform: scale(1) translateY(0) rotate(0deg);
        opacity: 1;
      }
      100% {
        transform: scale(0.8) translateY(-50px) rotate(5deg);
        opacity: 0;
      }
    }

    .custom-alert-box.animate-out {
      animation: alertSlideOut 0.3s ease-in-out;
    }

    /* Floating particles in alert */
    .alert-particle {
      position: absolute;
      width: 4px;
      height: 4px;
      background: rgba(45, 80, 22, 0.6);
      border-radius: 50%;
      pointer-events: none;
      animation: floatParticle 3s infinite ease-in-out;
    }

    @keyframes floatParticle {
      0%, 100% {
        transform: translateY(0px) rotate(0deg);
        opacity: 0.6;
      }
      50% {
        transform: translateY(-20px) rotate(180deg);
        opacity: 1;
      }
    }

    /* Mobile Responsive Styles */
    @media (max-width: 768px) {
      /* Navigation Mobile Optimizations */
      .navbar-glass {
        padding: 0 1rem;
      }

      .navbar-glass .flex {
        height: 70px;
      }

      .navbar-glass .text-2xl {
        font-size: 1.5rem;
      }

      /* Hero Section Mobile */
      #home h1 {
        font-size: 2.5rem !important;
        line-height: 1.2;
        margin-bottom: 1.5rem;
      }

      #home p {
        font-size: 1.1rem !important;
        margin-bottom: 2rem;
      }

      #home .flex-col {
        gap: 1rem;
      }

      #home .luxury-btn {
        width: 100%;
        max-width: 280px;
        padding: 14px 24px;
        font-size: 0.9rem;
      }

      #home .glass-effect {
        width: 100%;
        max-width: 280px;
        padding: 14px 24px;
        font-size: 0.9rem;
      }

      /* Section Spacing Mobile */
      section {
        padding: 3rem 1rem !important;
      }

      /* Typography Mobile */
      .text-5xl, .text-6xl {
        font-size: 2.5rem !important;
      }

      .text-4xl {
        font-size: 2rem !important;
      }

      .text-3xl {
        font-size: 1.75rem !important;
      }

      .text-2xl {
        font-size: 1.5rem !important;
      }

      .text-xl {
        font-size: 1.125rem !important;
      }

      /* Grid Layouts Mobile */
      .grid.lg\\:grid-cols-2 {
        grid-template-columns: 1fr !important;
        gap: 2rem;
      }

      .grid.md\\:grid-cols-2 {
        grid-template-columns: 1fr !important;
        gap: 1.5rem;
      }

      .grid.md\\:grid-cols-3 {
        grid-template-columns: 1fr !important;
        gap: 1.5rem;
      }

      .grid.md\\:grid-cols-4 {
        grid-template-columns: repeat(2, 1fr) !important;
        gap: 1rem;
      }

      /* About Section Mobile */
      #about img {
        height: 250px;
      }

      /* Gallery Mobile */
      .gallery-main {
        height: 300px;
      }

      .gallery-main img {
        height: 300px;
      }

      /* Contact Form Mobile */
      .contact-form {
        padding: 1.5rem !important;
      }

      .form-grid {
        grid-template-columns: 1fr !important;
        gap: 1rem;
      }

      input, textarea, select {
        padding: 12px 16px !important;
        font-size: 1rem;
      }

      /* CTA Section Mobile */
      .flex.md\\:flex-row {
        flex-direction: column !important;
        gap: 1rem !important;
      }

      /* Footer Mobile */
      .grid.md\\:grid-cols-4 {
        grid-template-columns: 1fr !important;
        gap: 2rem;
        text-align: center;
      }

      /* Custom Alert Mobile */
      .custom-alert-box {
        margin: 1rem;
        padding: 1.5rem;
        max-width: calc(100vw - 2rem);
      }

      .custom-alert-title {
        font-size: 1.5rem;
      }

      .custom-alert-message {
        font-size: 0.95rem;
      }

      .custom-alert-buttons {
        flex-direction: column;
        gap: 0.75rem;
      }

      .custom-alert-btn {
        width: 100%;
        padding: 14px 24px;
      }

      /* Floating WhatsApp Mobile */
      .floating-whatsapp {
        bottom: 20px;
        right: 20px;
        width: 50px;
        height: 50px;
        font-size: 20px;
      }

      .whatsapp-tooltip {
        display: none !important;
      }



      /* Scroll Progress Mobile */
      #scrollProgress {
        height: 3px;
      }

      /* Particle Effects Mobile - Reduce */
      .particle {
        display: none;
      }

      .floating {
        animation-duration: 8s;
      }

      /* Hover Effects Mobile - Simplify */
      .hover-lift:hover {
        transform: translateY(-5px) scale(1.01);
      }

      .tilt-effect:hover {
        transform: none;
      }

      .magnetic:hover {
        transform: scale(1.02);
      }

      /* Background Elements Mobile - Reduce */
      .absolute.w-64,
      .absolute.w-48,
      .absolute.w-32,
      .absolute.w-24 {
        opacity: 0.05 !important;
      }
    }

    @media (max-width: 480px) {
      /* Extra Small Mobile */
      #home h1 {
        font-size: 2rem !important;
      }

      .text-5xl, .text-6xl {
        font-size: 2rem !important;
      }

      .text-4xl {
        font-size: 1.75rem !important;
      }

      section {
        padding: 2rem 0.75rem !important;
      }

      .custom-alert-box {
        margin: 0.5rem;
        padding: 1rem;
      }

      .luxury-btn {
        padding: 12px 20px;
        font-size: 0.85rem;
      }

      .floating-whatsapp {
        width: 45px;
        height: 45px;
        font-size: 18px;
        bottom: 15px;
        right: 15px;
      }

      .grid.md\\:grid-cols-4 {
        grid-template-columns: 1fr !important;
      }
    }

    /* Tablet Responsive */
    @media (min-width: 769px) and (max-width: 1024px) {
      #home h1 {
        font-size: 4rem !important;
      }

      .text-5xl, .text-6xl {
        font-size: 3.5rem !important;
      }

      .grid.md\\:grid-cols-3 {
        grid-template-columns: repeat(2, 1fr) !important;
      }

      .grid.md\\:grid-cols-4 {
        grid-template-columns: repeat(3, 1fr) !important;
      }
    }

    /* Touch Device Optimizations */
    @media (hover: none) and (pointer: coarse) {
      .hover-lift:hover {
        transform: none;
      }

      .tilt-effect:hover {
        transform: none;
      }

      .magnetic:hover {
        transform: none;
      }

      .luxury-btn:hover {
        transform: none;
        box-shadow: 0 5px 15px rgba(212, 175, 55, 0.3);
      }

      .nav-link:hover {
        color: inherit;
      }



      /* Simplify animations for touch devices */
      .glow-effect {
        animation: none;
      }

      .floating {
        animation-duration: 10s;
      }
    }

    /* High DPI Displays */
    @media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
      .luxury-shadow {
        box-shadow: 0 12px 25px -6px rgba(0, 0, 0, 0.15), 0 0 0 1px rgba(255, 255, 255, 0.05);
      }
    }

    /* Landscape Mobile */
    @media (max-width: 768px) and (orientation: landscape) {
      #home {
        height: auto;
        min-height: 100vh;
        padding: 2rem 1rem;
      }

      #home h1 {
        font-size: 2.25rem !important;
        margin-bottom: 1rem;
      }

      #home p {
        font-size: 1rem !important;
        margin-bottom: 1.5rem;
      }

      .navbar-glass .flex {
        height: 60px;
      }
    }
  </style>

  <link rel="icon" href="/favicon.ico" />
</head>
<body class="text-gray-900 font-sans bg-white">



<!-- Scroll Progress Indicator -->
<div class="fixed top-0 left-0 w-full h-1 bg-gray-900 z-50">
  <div class="h-full bg-gradient-to-r from-green-600 via-green-500 to-green-700 transition-all duration-300 ease-out" id="scrollProgress" style="width: 0%;"></div>
</div>

<!-- Custom Alert Box -->
<div class="custom-alert-overlay" id="customAlertOverlay">
  <div class="custom-alert-box" id="customAlertBox">
    <div class="custom-alert-icon" id="customAlertIcon">
      <i class="fas fa-check" id="customAlertIconSymbol"></i>
    </div>
    <div class="custom-alert-title" id="customAlertTitle">Success!</div>
    <div class="custom-alert-message" id="customAlertMessage">Your message has been sent successfully.</div>
    <div class="custom-alert-buttons" id="customAlertButtons">
      <button class="custom-alert-btn custom-alert-btn-primary" id="customAlertOkBtn">OK</button>
    </div>
  </div>
</div>

<!-- Navigation -->
<nav class="fixed top-0 w-full z-50 navbar-glass transition-all duration-500 transform" id="navbar">
  <div class="max-w-7xl mx-auto px-6 lg:px-8">
    <div class="flex justify-between items-center h-20">
      <div class="flex items-center">
        <div class="text-2xl font-playfair font-bold magnetic glow-effect">
          <span class="gradient-text">Green Alley</span>
        </div>
      </div>

      <div class="hidden md:flex items-center space-x-8">
        <a href="#home" class="nav-link text-white hover:text-green-400 transition-all duration-300 font-medium relative overflow-hidden">
          <span class="relative z-10">Home</span>
          <div class="absolute bottom-0 left-0 w-0 h-0.5 bg-gradient-to-r from-green-600 to-green-700 transition-all duration-300 hover:w-full"></div>
        </a>
        <a href="#about" class="nav-link text-white hover:text-green-400 transition-all duration-300 font-medium relative overflow-hidden">
          <span class="relative z-10">About</span>
          <div class="absolute bottom-0 left-0 w-0 h-0.5 bg-gradient-to-r from-green-600 to-green-700 transition-all duration-300 hover:w-full"></div>
        </a>
        <a href="#gallery" class="nav-link text-white hover:text-green-400 transition-all duration-300 font-medium relative overflow-hidden">
          <span class="relative z-10">Gallery</span>
          <div class="absolute bottom-0 left-0 w-0 h-0.5 bg-gradient-to-r from-green-600 to-green-700 transition-all duration-300 hover:w-full"></div>
        </a>
        <a href="#amenities" class="nav-link text-white hover:text-green-400 transition-all duration-300 font-medium relative overflow-hidden">
          <span class="relative z-10">Amenities</span>
          <div class="absolute bottom-0 left-0 w-0 h-0.5 bg-gradient-to-r from-green-600 to-green-700 transition-all duration-300 hover:w-full"></div>
        </a>
        <a href="#location" class="nav-link text-white hover:text-green-400 transition-all duration-300 font-medium relative overflow-hidden">
          <span class="relative z-10">Location</span>
          <div class="absolute bottom-0 left-0 w-0 h-0.5 bg-gradient-to-r from-green-600 to-green-700 transition-all duration-300 hover:w-full"></div>
        </a>
        <a href="#contact" class="nav-link text-white hover:text-green-400 transition-all duration-300 font-medium relative overflow-hidden">
          <span class="relative z-10">Contact</span>
          <div class="absolute bottom-0 left-0 w-0 h-0.5 bg-gradient-to-r from-green-600 to-green-700 transition-all duration-300 hover:w-full"></div>
        </a>
        <a href="#enquiry" class="luxury-btn text-sm morph-btn magnetic">Book Visit</a>
      </div>

      <div class="md:hidden">
        <button id="mobile-menu-btn" class="text-white focus:outline-none magnetic">
          <div class="hamburger-menu">
            <span class="line line1"></span>
            <span class="line line2"></span>
            <span class="line line3"></span>
          </div>
        </button>
      </div>
    </div>
  </div>

  <!-- Mobile Menu -->
  <div id="mobile-menu" class="md:hidden hidden bg-black bg-opacity-95 backdrop-blur-lg transform transition-all duration-500 translate-y-full">
    <div class="px-6 py-4 space-y-4">
      <a href="#home" class="block text-white hover:text-green-400 transition-all duration-300 font-medium transform hover:translateX-2">Home</a>
      <a href="#about" class="block text-white hover:text-green-400 transition-all duration-300 font-medium transform hover:translateX-2">About</a>
      <a href="#gallery" class="block text-white hover:text-green-400 transition-all duration-300 font-medium transform hover:translateX-2">Gallery</a>
      <a href="#amenities" class="block text-white hover:text-green-400 transition-all duration-300 font-medium transform hover:translateX-2">Amenities</a>
      <a href="#location" class="block text-white hover:text-green-400 transition-all duration-300 font-medium transform hover:translateX-2">Location</a>
      <a href="#contact" class="block text-white hover:text-green-400 transition-all duration-300 font-medium transform hover:translateX-2">Contact</a>
      <a href="#enquiry" class="luxury-btn text-sm inline-block mt-4 morph-btn">Book Visit</a>
    </div>
  </div>
</nav>

<style>
.nav-link:hover .absolute {
  width: 100%;
}

.hamburger-menu {
  width: 24px;
  height: 18px;
  position: relative;
  cursor: pointer;
}

.line {
  display: block;
  height: 2px;
  width: 100%;
  background: white;
  position: absolute;
  transition: all 0.3s ease;
}

.line1 { top: 0; }
.line2 { top: 50%; transform: translateY(-50%); }
.line3 { bottom: 0; }

.hamburger-menu.active .line1 {
  transform: rotate(45deg);
  top: 50%;
  margin-top: -1px;
}

.hamburger-menu.active .line2 {
  opacity: 0;
}

.hamburger-menu.active .line3 {
  transform: rotate(-45deg);
  bottom: 50%;
  margin-bottom: -1px;
}
</style>

<!-- Hero Section -->
<section id="home" class="relative h-screen w-full overflow-hidden particle-bg">
  <!-- Background Video/Image -->
  <div class="absolute inset-0 z-0">
    <div class="w-full h-full bg-gradient-to-br from-gray-900 via-green-900 to-black"></div>
    <!-- Placeholder for video - you can add actual video here -->
    <div class="absolute inset-0 bg-cover bg-center bg-no-repeat opacity-40 parallax"
         style="background-image: url('https://images.unsplash.com/photo-1600596542815-ffad4c1539a9?ixlib=rb-4.0.3&auto=format&fit=crop&w=2070&q=80');" data-speed="0.5">
    </div>
  </div>

  <!-- Particle Effects -->
  <div class="absolute inset-0 z-5">
    <div class="particle" style="left: 10%; animation-delay: 0s;"></div>
    <div class="particle" style="left: 20%; animation-delay: 2s;"></div>
    <div class="particle" style="left: 30%; animation-delay: 4s;"></div>
    <div class="particle" style="left: 40%; animation-delay: 6s;"></div>
    <div class="particle" style="left: 50%; animation-delay: 8s;"></div>
    <div class="particle" style="left: 60%; animation-delay: 10s;"></div>
    <div class="particle" style="left: 70%; animation-delay: 12s;"></div>
    <div class="particle" style="left: 80%; animation-delay: 14s;"></div>
    <div class="particle" style="left: 90%; animation-delay: 16s;"></div>
  </div>

  <!-- Hero Overlay -->
  <div class="absolute inset-0 hero-overlay z-10 liquid-bg"></div>

  <!-- Hero Content -->
  <div class="relative z-20 flex flex-col justify-center items-center text-center h-full px-6 text-white">
    <div class="max-w-5xl mx-auto" data-aos="fade-up" data-aos-duration="1000">
      <h1 class="text-5xl md:text-7xl lg:text-8xl font-playfair font-bold mb-6 leading-tight">
        <span class="inline-block" data-aos="slide-down" data-aos-delay="200">Welcome to</span> <br>
        <span class="gradient-text inline-block glow-effect" data-aos="zoom-in" data-aos-delay="600">Green Alley</span>
      </h1>
      <p class="text-xl md:text-2xl lg:text-3xl mb-8 max-w-3xl mx-auto font-light leading-relaxed" data-aos="fade-up" data-aos-delay="800">
        Experience Unparalleled <span class="text-green-400 font-semibold">Luxury Living</span> in the Green Heart of Pune
      </p>
      <div class="flex flex-col md:flex-row justify-center items-center space-y-4 md:space-y-0 md:space-x-6" data-aos="fade-up" data-aos-delay="1000">
        <a href="#enquiry" class="luxury-btn morph-btn magnetic scale-in">
          <i class="fas fa-calendar-check mr-2"></i>
          Book Site Visit
        </a>
        <a href="#gallery" class="glass-effect px-8 py-4 rounded-full text-white hover:bg-white hover:text-black transition-all duration-500 font-semibold tilt-effect magnetic">
          <i class="fas fa-play mr-2"></i>
          View Gallery
        </a>
      </div>
    </div>

    <!-- Scroll Indicator -->
    <div class="absolute bottom-8 left-1/2 transform -translate-x-1/2 text-white animate-bounce magnetic" data-aos="fade-up" data-aos-delay="1200">
      <div class="flex flex-col items-center">
        <span class="text-sm mb-2 opacity-75">Scroll Down</span>
        <i class="fas fa-chevron-down text-2xl glow-effect"></i>
      </div>
    </div>
  </div>

  <!-- Enhanced Floating Elements -->
  <div class="absolute top-20 left-10 w-20 h-20 bg-green-600 rounded-full opacity-20 floating glow-effect"></div>
  <div class="absolute bottom-20 right-10 w-16 h-16 bg-green-400 rounded-full opacity-20 floating glow-effect" style="animation-delay: 3s;"></div>
  <div class="absolute top-1/2 right-20 w-12 h-12 bg-green-500 rounded-full opacity-15 floating" style="animation-delay: 5s;"></div>
  <div class="absolute bottom-1/3 left-20 w-8 h-8 bg-green-300 rounded-full opacity-15 floating" style="animation-delay: 7s;"></div>

  <!-- Geometric Shapes -->
  <div class="absolute top-1/4 left-1/4 w-32 h-32 border-2 border-green-600 opacity-10 rotate-45 floating"></div>
  <div class="absolute bottom-1/4 right-1/4 w-24 h-24 border-2 border-green-400 opacity-10 rounded-full floating" style="animation-delay: 4s;"></div>
</section>

<!-- About Section -->
<section id="about" class="py-20 px-6 md:px-16 bg-white relative overflow-hidden">
  <div class="max-w-7xl mx-auto">
    <div class="text-center mb-16" data-aos="fade-up">
      <h2 class="text-5xl md:text-6xl font-playfair font-bold mb-6 text-gray-900">
        Prime Location. <span class="gradient-text">Peaceful Living.</span> Premium Lifestyle.
      </h2>
      <div class="w-24 h-1 mx-auto mb-8" style="background: linear-gradient(135deg, #D4AF37 0%, #FFD700 50%, #B8860B 100%);"></div>
      <p class="max-w-4xl mx-auto text-xl text-gray-600 leading-relaxed">
        Nestled beside lush army greens in Pimple Nilakh, Green Alley offers 4 BHK designer villas with uninterrupted views, exceptional connectivity, and sophisticated living that redefines luxury.
      </p>
    </div>

    <div class="grid lg:grid-cols-2 gap-16 items-center">
      <div data-aos="fade-right" data-aos-duration="1000">
        <div class="relative group">
          <div class="absolute inset-0 bg-gradient-to-r from-yellow-400 to-yellow-600 rounded-2xl blur-lg opacity-25 group-hover:opacity-40 transition-opacity duration-500"></div>
          <img src="https://images.unsplash.com/photo-1600607687939-ce8a6c25118c?ixlib=rb-4.0.3&auto=format&fit=crop&w=1000&q=80"
               alt="Luxury Villa"
               class="relative rounded-2xl luxury-shadow w-full h-96 object-cover hover-lift tilt-effect">
          <div class="absolute -bottom-6 -right-6 w-32 h-32 luxury-gradient rounded-2xl flex items-center justify-center glow-effect rotate-in">
            <div class="text-center text-white">
              <div class="text-2xl font-bold counter" data-target="4">0</div>
              <div class="text-sm">BHK Villas</div>
            </div>
          </div>
          <!-- Overlay on hover -->
          <div class="absolute inset-0 bg-black bg-opacity-50 rounded-2xl opacity-0 group-hover:opacity-100 transition-opacity duration-500 flex items-center justify-center">
            <div class="text-white text-center">
              <i class="fas fa-search-plus text-3xl mb-2"></i>
              <p class="text-lg font-semibold">View Details</p>
            </div>
          </div>
        </div>
      </div>

      <div data-aos="fade-left" data-aos-duration="1000">
        <h3 class="text-3xl font-playfair font-bold mb-6 text-gray-900 text-reveal">Why Choose Green Alley?</h3>
        <div class="space-y-6">
          <div class="flex items-start space-x-4 group hover-lift" data-aos="slide-in-right" data-aos-delay="200">
            <div class="w-12 h-12 luxury-gradient rounded-full flex items-center justify-center flex-shrink-0 magnetic glow-effect">
              <i class="fas fa-home text-white transition-transform duration-300 group-hover:scale-110"></i>
            </div>
            <div class="flex-1">
              <h4 class="text-xl font-semibold mb-2 text-gray-900 group-hover:text-yellow-600 transition-colors duration-300">Designer Villas</h4>
              <p class="text-gray-600 group-hover:text-gray-700 transition-colors duration-300">Spacious 4 BHK villas with contemporary architecture and premium finishes</p>
            </div>
          </div>

          <div class="flex items-start space-x-4 group hover-lift" data-aos="slide-in-right" data-aos-delay="400">
            <div class="w-12 h-12 luxury-gradient rounded-full flex items-center justify-center flex-shrink-0 magnetic glow-effect">
              <i class="fas fa-tree text-white transition-transform duration-300 group-hover:scale-110"></i>
            </div>
            <div class="flex-1">
              <h4 class="text-xl font-semibold mb-2 text-gray-900 group-hover:text-yellow-600 transition-colors duration-300">Green Views</h4>
              <p class="text-gray-600 group-hover:text-gray-700 transition-colors duration-300">Facing army green zone with guaranteed nature views for lifetime</p>
            </div>
          </div>

          <div class="flex items-start space-x-4 group hover-lift" data-aos="slide-in-right" data-aos-delay="600">
            <div class="w-12 h-12 luxury-gradient rounded-full flex items-center justify-center flex-shrink-0 magnetic glow-effect">
              <i class="fas fa-shield-alt text-white transition-transform duration-300 group-hover:scale-110"></i>
            </div>
            <div class="flex-1">
              <h4 class="text-xl font-semibold mb-2 text-gray-900 group-hover:text-yellow-600 transition-colors duration-300">Premium Security</h4>
              <p class="text-gray-600 group-hover:text-gray-700 transition-colors duration-300">24x7 security with CCTV surveillance and gated community</p>
            </div>
          </div>

          <div class="flex items-start space-x-4 group hover-lift" data-aos="slide-in-right" data-aos-delay="800">
            <div class="w-12 h-12 luxury-gradient rounded-full flex items-center justify-center flex-shrink-0 magnetic glow-effect">
              <i class="fas fa-wifi text-white transition-transform duration-300 group-hover:scale-110"></i>
            </div>
            <div class="flex-1">
              <h4 class="text-xl font-semibold mb-2 text-gray-900 group-hover:text-yellow-600 transition-colors duration-300">Smart Ready</h4>
              <p class="text-gray-600 group-hover:text-gray-700 transition-colors duration-300">Modern automation-ready infrastructure for smart living</p>
            </div>
          </div>
        </div>

        <!-- Progress Bars -->
        <div class="mt-8 space-y-4" data-aos="fade-up" data-aos-delay="1000">
          <div class="progress-item">
            <div class="flex justify-between mb-2">
              <span class="text-sm font-semibold text-gray-700">Luxury Features</span>
              <span class="text-sm text-gray-600">95%</span>
            </div>
            <div class="w-full bg-gray-200 rounded-full h-2">
              <div class="bg-gradient-to-r from-yellow-400 to-yellow-600 h-2 rounded-full progress-bar" data-width="95"></div>
            </div>
          </div>
          <div class="progress-item">
            <div class="flex justify-between mb-2">
              <span class="text-sm font-semibold text-gray-700">Location Advantage</span>
              <span class="text-sm text-gray-600">90%</span>
            </div>
            <div class="w-full bg-gray-200 rounded-full h-2">
              <div class="bg-gradient-to-r from-green-400 to-green-600 h-2 rounded-full progress-bar" data-width="90"></div>
            </div>
          </div>
          <div class="progress-item">
            <div class="flex justify-between mb-2">
              <span class="text-sm font-semibold text-gray-700">Investment Value</span>
              <span class="text-sm text-gray-600">98%</span>
            </div>
            <div class="w-full bg-gray-200 rounded-full h-2">
              <div class="bg-gradient-to-r from-blue-400 to-blue-600 h-2 rounded-full progress-bar" data-width="98"></div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Background Elements -->
  <div class="absolute top-0 right-0 w-64 h-64 bg-gradient-to-br from-yellow-200 to-yellow-400 rounded-full opacity-10 -translate-y-32 translate-x-32"></div>
  <div class="absolute bottom-0 left-0 w-48 h-48 bg-gradient-to-tr from-green-200 to-green-400 rounded-full opacity-10 translate-y-24 -translate-x-24"></div>
</section>

<!-- Location Section -->
<section id="location" class="py-20 px-6 md:px-16 bg-white relative overflow-hidden">
  <div class="max-w-7xl mx-auto">
    <div class="text-center mb-16" data-aos="fade-up">
      <h2 class="text-5xl md:text-6xl font-playfair font-bold mb-6 text-gray-900">
        Prime <span class="gradient-text">Location</span>
      </h2>
      <div class="w-24 h-1 mx-auto mb-8" style="background: linear-gradient(135deg, #D4AF37 0%, #FFD700 50%, #B8860B 100%);"></div>
      <p class="max-w-3xl mx-auto text-xl text-gray-600 leading-relaxed">
        Strategically located in Pimple Nilakh with excellent connectivity to all major landmarks
      </p>
    </div>

    <div class="grid lg:grid-cols-2 gap-16 items-start mb-16">
      <!-- Location Highlights -->
      <div data-aos="fade-right">
        <div class="grid md:grid-cols-2 gap-8">
          <!-- Hospitals -->
          <div class="bg-gradient-to-br from-red-50 to-red-100 rounded-2xl p-6 hover-lift">
            <div class="flex items-center mb-4">
              <div class="w-12 h-12 bg-red-500 rounded-xl flex items-center justify-center mr-4">
                <i class="fas fa-hospital text-white"></i>
              </div>
              <h3 class="text-xl font-bold text-gray-900">Healthcare</h3>
            </div>
            <ul class="space-y-3 text-gray-700">
              <li class="flex items-center">
                <i class="fas fa-map-marker-alt text-red-500 mr-2"></i>
                Jupiter Hospital – 10 mins
              </li>
              <li class="flex items-center">
                <i class="fas fa-map-marker-alt text-red-500 mr-2"></i>
                Surya Mother & Child – 7 mins
              </li>
              <li class="flex items-center">
                <i class="fas fa-map-marker-alt text-red-500 mr-2"></i>
                Lifepoint Wakad – 10 mins
              </li>
              <li class="flex items-center">
                <i class="fas fa-map-marker-alt text-red-500 mr-2"></i>
                Aditya Birla Hospital – 15 mins
              </li>
            </ul>
          </div>

          <!-- Education -->
          <div class="bg-gradient-to-br from-blue-50 to-blue-100 rounded-2xl p-6 hover-lift">
            <div class="flex items-center mb-4">
              <div class="w-12 h-12 bg-blue-500 rounded-xl flex items-center justify-center mr-4">
                <i class="fas fa-graduation-cap text-white"></i>
              </div>
              <h3 class="text-xl font-bold text-gray-900">Education</h3>
            </div>
            <ul class="space-y-3 text-gray-700">
              <li class="flex items-center">
                <i class="fas fa-map-marker-alt text-blue-500 mr-2"></i>
                Wisdom World Wakad – 10 mins
              </li>
              <li class="flex items-center">
                <i class="fas fa-map-marker-alt text-blue-500 mr-2"></i>
                Indira National – 15 mins
              </li>
              <li class="flex items-center">
                <i class="fas fa-map-marker-alt text-blue-500 mr-2"></i>
                MITCON Balewadi – 7 mins
              </li>
              <li class="flex items-center">
                <i class="fas fa-map-marker-alt text-blue-500 mr-2"></i>
                International Schools Nearby
              </li>
            </ul>
          </div>

          <!-- Shopping -->
          <div class="bg-gradient-to-br from-purple-50 to-purple-100 rounded-2xl p-6 hover-lift">
            <div class="flex items-center mb-4">
              <div class="w-12 h-12 bg-purple-500 rounded-xl flex items-center justify-center mr-4">
                <i class="fas fa-shopping-bag text-white"></i>
              </div>
              <h3 class="text-xl font-bold text-gray-900">Shopping</h3>
            </div>
            <ul class="space-y-3 text-gray-700">
              <li class="flex items-center">
                <i class="fas fa-map-marker-alt text-purple-500 mr-2"></i>
                Westend Mall Aundh – 10 mins
              </li>
              <li class="flex items-center">
                <i class="fas fa-map-marker-alt text-purple-500 mr-2"></i>
                Phoenix Marketcity – 12 mins
              </li>
              <li class="flex items-center">
                <i class="fas fa-map-marker-alt text-purple-500 mr-2"></i>
                Balewadi High Street – 10 mins
              </li>
              <li class="flex items-center">
                <i class="fas fa-map-marker-alt text-purple-500 mr-2"></i>
                Local Markets Nearby
              </li>
            </ul>
          </div>

          <!-- Transport -->
          <div class="bg-gradient-to-br from-green-50 to-green-100 rounded-2xl p-6 hover-lift">
            <div class="flex items-center mb-4">
              <div class="w-12 h-12 bg-green-500 rounded-xl flex items-center justify-center mr-4">
                <i class="fas fa-subway text-white"></i>
              </div>
              <h3 class="text-xl font-bold text-gray-900">Transport</h3>
            </div>
            <ul class="space-y-3 text-gray-700">
              <li class="flex items-center">
                <i class="fas fa-map-marker-alt text-green-500 mr-2"></i>
                Metro Line 3 – 1.5 km
              </li>
              <li class="flex items-center">
                <i class="fas fa-map-marker-alt text-green-500 mr-2"></i>
                Mumbai-Pune Expressway – 10 mins
              </li>
              <li class="flex items-center">
                <i class="fas fa-map-marker-alt text-green-500 mr-2"></i>
                BRT Corridor – Nearby
              </li>
              <li class="flex items-center">
                <i class="fas fa-map-marker-alt text-green-500 mr-2"></i>
                Airport – 45 mins
              </li>
            </ul>
          </div>
        </div>
      </div>

      <!-- Interactive Map -->
      <div data-aos="fade-left">
        <div class="bg-white rounded-2xl luxury-shadow overflow-hidden">
          <div class="p-6 bg-gradient-to-r from-gray-900 to-gray-800">
            <h3 class="text-2xl font-bold text-white mb-2">Interactive Location Map</h3>
            <p class="text-gray-300">Explore the neighborhood and nearby amenities</p>
          </div>
          <div class="relative">
            <iframe class="w-full h-96"
              src="https://www.google.com/maps/embed?pb=!1m18!1m12!1m3!1d3782.0362663249555!2d73.7855!3d18.5669!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0x3bc2b94e3e121f7f%3A0x7ad58f3!2sPimple%20Nilakh%2C%20Pune%2C%20Maharashtra!5e0!3m2!1sen!2sin!4v1719821234567"
              allowfullscreen="" loading="lazy"></iframe>
            <div class="absolute top-4 left-4 bg-white rounded-lg p-3 luxury-shadow">
              <div class="flex items-center">
                <div class="w-3 h-3 bg-red-500 rounded-full mr-2"></div>
                <span class="text-sm font-semibold text-gray-900">Green Alley</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Background Elements -->
  <div class="absolute top-0 left-0 w-64 h-64 bg-gradient-to-br from-yellow-200 to-yellow-400 rounded-full opacity-10 -translate-y-32 -translate-x-32"></div>
  <div class="absolute bottom-0 right-0 w-48 h-48 bg-gradient-to-tl from-green-200 to-green-400 rounded-full opacity-10 translate-y-24 translate-x-24"></div>
</section>

<!-- Amenities Section -->
<section id="amenities" class="py-20 px-6 md:px-16 bg-gray-50 relative overflow-hidden">
  <div class="max-w-7xl mx-auto">
    <div class="text-center mb-16" data-aos="fade-up">
      <h2 class="text-5xl md:text-6xl font-playfair font-bold mb-6 text-gray-900">
        Luxury <span class="gradient-text">Amenities</span>
      </h2>
      <div class="w-24 h-1 mx-auto mb-8" style="background: linear-gradient(135deg, #D4AF37 0%, #FFD700 50%, #B8860B 100%);"></div>
      <p class="max-w-3xl mx-auto text-xl text-gray-600 leading-relaxed">
        Experience world-class amenities designed for the discerning homeowner
      </p>
    </div>

    <div class="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
      <!-- Amenity Card 1 -->
      <div class="group bg-white rounded-2xl p-8 luxury-shadow hover-lift tilt-effect magnetic" data-aos="flip-left" data-aos-delay="100">
        <div class="relative">
          <div class="absolute inset-0 bg-gradient-to-r from-yellow-400 to-yellow-600 rounded-2xl blur-md opacity-0 group-hover:opacity-30 transition-opacity duration-500"></div>
          <div class="relative w-16 h-16 luxury-gradient rounded-2xl flex items-center justify-center mb-6 group-hover:scale-110 transition-transform duration-300 glow-effect rotate-in">
            <i class="fas fa-home text-white text-2xl group-hover:rotate-12 transition-transform duration-300"></i>
          </div>
        </div>
        <h3 class="text-2xl font-bold mb-4 text-gray-900 group-hover:text-yellow-600 transition-colors duration-300">4 BHK Designer Villas</h3>
        <p class="text-gray-600 leading-relaxed group-hover:text-gray-700 transition-colors duration-300">Spacious, airy interiors with luxury finishes and contemporary architecture</p>
        <div class="mt-4 opacity-0 group-hover:opacity-100 transition-opacity duration-300">
          <div class="w-full bg-gray-200 rounded-full h-1">
            <div class="bg-gradient-to-r from-yellow-400 to-yellow-600 h-1 rounded-full w-0 group-hover:w-full transition-all duration-1000"></div>
          </div>
        </div>
      </div>

      <!-- Amenity Card 2 -->
      <div class="group bg-white rounded-2xl p-8 luxury-shadow hover-lift tilt-effect magnetic" data-aos="flip-left" data-aos-delay="200">
        <div class="relative">
          <div class="absolute inset-0 bg-gradient-to-r from-green-400 to-green-600 rounded-2xl blur-md opacity-0 group-hover:opacity-30 transition-opacity duration-500"></div>
          <div class="relative w-16 h-16 luxury-gradient rounded-2xl flex items-center justify-center mb-6 group-hover:scale-110 transition-transform duration-300 glow-effect rotate-in">
            <i class="fas fa-tree text-white text-2xl group-hover:rotate-12 transition-transform duration-300"></i>
          </div>
        </div>
        <h3 class="text-2xl font-bold mb-4 text-gray-900 group-hover:text-yellow-600 transition-colors duration-300">Army Green Zone Views</h3>
        <p class="text-gray-600 leading-relaxed group-hover:text-gray-700 transition-colors duration-300">Guaranteed green views for life with unobstructed nature vistas</p>
        <div class="mt-4 opacity-0 group-hover:opacity-100 transition-opacity duration-300">
          <div class="w-full bg-gray-200 rounded-full h-1">
            <div class="bg-gradient-to-r from-green-400 to-green-600 h-1 rounded-full w-0 group-hover:w-full transition-all duration-1000"></div>
          </div>
        </div>
      </div>

      <!-- Amenity Card 3 -->
      <div class="group bg-white rounded-2xl p-8 luxury-shadow hover-lift tilt-effect magnetic" data-aos="flip-left" data-aos-delay="300">
        <div class="relative">
          <div class="absolute inset-0 bg-gradient-to-r from-blue-400 to-blue-600 rounded-2xl blur-md opacity-0 group-hover:opacity-30 transition-opacity duration-500"></div>
          <div class="relative w-16 h-16 luxury-gradient rounded-2xl flex items-center justify-center mb-6 group-hover:scale-110 transition-transform duration-300 glow-effect rotate-in">
            <i class="fas fa-car text-white text-2xl group-hover:rotate-12 transition-transform duration-300"></i>
          </div>
        </div>
        <h3 class="text-2xl font-bold mb-4 text-gray-900 group-hover:text-yellow-600 transition-colors duration-300">Private Parking</h3>
        <p class="text-gray-600 leading-relaxed group-hover:text-gray-700 transition-colors duration-300">Dedicated covered car park per unit with additional guest parking</p>
        <div class="mt-4 opacity-0 group-hover:opacity-100 transition-opacity duration-300">
          <div class="w-full bg-gray-200 rounded-full h-1">
            <div class="bg-gradient-to-r from-blue-400 to-blue-600 h-1 rounded-full w-0 group-hover:w-full transition-all duration-1000"></div>
          </div>
        </div>
      </div>

      <!-- Amenity Card 4 -->
      <div class="group bg-white rounded-2xl p-8 luxury-shadow hover-lift tilt-effect magnetic" data-aos="flip-left" data-aos-delay="400">
        <div class="relative">
          <div class="absolute inset-0 bg-gradient-to-r from-purple-400 to-purple-600 rounded-2xl blur-md opacity-0 group-hover:opacity-30 transition-opacity duration-500"></div>
          <div class="relative w-16 h-16 luxury-gradient rounded-2xl flex items-center justify-center mb-6 group-hover:scale-110 transition-transform duration-300 glow-effect rotate-in">
            <i class="fas fa-seedling text-white text-2xl group-hover:rotate-12 transition-transform duration-300"></i>
          </div>
        </div>
        <h3 class="text-2xl font-bold mb-4 text-gray-900 group-hover:text-yellow-600 transition-colors duration-300">Terrace Gardens</h3>
        <p class="text-gray-600 leading-relaxed group-hover:text-gray-700 transition-colors duration-300">Private terrace gardens with panoramic views of nature</p>
        <div class="mt-4 opacity-0 group-hover:opacity-100 transition-opacity duration-300">
          <div class="w-full bg-gray-200 rounded-full h-1">
            <div class="bg-gradient-to-r from-purple-400 to-purple-600 h-1 rounded-full w-0 group-hover:w-full transition-all duration-1000"></div>
          </div>
        </div>
      </div>

      <!-- Amenity Card 5 -->
      <div class="group bg-white rounded-2xl p-8 luxury-shadow hover-lift tilt-effect magnetic" data-aos="flip-left" data-aos-delay="500">
        <div class="relative">
          <div class="absolute inset-0 bg-gradient-to-r from-red-400 to-red-600 rounded-2xl blur-md opacity-0 group-hover:opacity-30 transition-opacity duration-500"></div>
          <div class="relative w-16 h-16 luxury-gradient rounded-2xl flex items-center justify-center mb-6 group-hover:scale-110 transition-transform duration-300 glow-effect rotate-in">
            <i class="fas fa-shield-alt text-white text-2xl group-hover:rotate-12 transition-transform duration-300"></i>
          </div>
        </div>
        <h3 class="text-2xl font-bold mb-4 text-gray-900 group-hover:text-yellow-600 transition-colors duration-300">Gated & Secure</h3>
        <p class="text-gray-600 leading-relaxed group-hover:text-gray-700 transition-colors duration-300">24x7 security with CCTV surveillance and controlled access</p>
        <div class="mt-4 opacity-0 group-hover:opacity-100 transition-opacity duration-300">
          <div class="w-full bg-gray-200 rounded-full h-1">
            <div class="bg-gradient-to-r from-red-400 to-red-600 h-1 rounded-full w-0 group-hover:w-full transition-all duration-1000"></div>
          </div>
        </div>
      </div>

      <!-- Amenity Card 6 -->
      <div class="group bg-white rounded-2xl p-8 luxury-shadow hover-lift tilt-effect magnetic" data-aos="flip-left" data-aos-delay="600">
        <div class="relative">
          <div class="absolute inset-0 bg-gradient-to-r from-indigo-400 to-indigo-600 rounded-2xl blur-md opacity-0 group-hover:opacity-30 transition-opacity duration-500"></div>
          <div class="relative w-16 h-16 luxury-gradient rounded-2xl flex items-center justify-center mb-6 group-hover:scale-110 transition-transform duration-300 glow-effect rotate-in">
            <i class="fas fa-wifi text-white text-2xl group-hover:rotate-12 transition-transform duration-300"></i>
          </div>
        </div>
        <h3 class="text-2xl font-bold mb-4 text-gray-900 group-hover:text-yellow-600 transition-colors duration-300">Smart Home Ready</h3>
        <p class="text-gray-600 leading-relaxed group-hover:text-gray-700 transition-colors duration-300">Modern automation-ready infrastructure for intelligent living</p>
        <div class="mt-4 opacity-0 group-hover:opacity-100 transition-opacity duration-300">
          <div class="w-full bg-gray-200 rounded-full h-1">
            <div class="bg-gradient-to-r from-indigo-400 to-indigo-600 h-1 rounded-full w-0 group-hover:w-full transition-all duration-1000"></div>
          </div>
        </div>
      </div>

      <!-- Amenity Card 7 -->
      <div class="group bg-white rounded-2xl p-8 luxury-shadow hover-lift tilt-effect magnetic" data-aos="flip-left" data-aos-delay="700">
        <div class="relative">
          <div class="absolute inset-0 bg-gradient-to-r from-cyan-400 to-cyan-600 rounded-2xl blur-md opacity-0 group-hover:opacity-30 transition-opacity duration-500"></div>
          <div class="relative w-16 h-16 luxury-gradient rounded-2xl flex items-center justify-center mb-6 group-hover:scale-110 transition-transform duration-300 glow-effect rotate-in">
            <i class="fas fa-swimming-pool text-white text-2xl group-hover:rotate-12 transition-transform duration-300"></i>
          </div>
        </div>
        <h3 class="text-2xl font-bold mb-4 text-gray-900 group-hover:text-yellow-600 transition-colors duration-300">Swimming Pool</h3>
        <p class="text-gray-600 leading-relaxed group-hover:text-gray-700 transition-colors duration-300">Resort-style swimming pool with deck area for relaxation</p>
        <div class="mt-4 opacity-0 group-hover:opacity-100 transition-opacity duration-300">
          <div class="w-full bg-gray-200 rounded-full h-1">
            <div class="bg-gradient-to-r from-cyan-400 to-cyan-600 h-1 rounded-full w-0 group-hover:w-full transition-all duration-1000"></div>
          </div>
        </div>
      </div>

      <!-- Amenity Card 8 -->
      <div class="group bg-white rounded-2xl p-8 luxury-shadow hover-lift tilt-effect magnetic" data-aos="flip-left" data-aos-delay="800">
        <div class="relative">
          <div class="absolute inset-0 bg-gradient-to-r from-orange-400 to-orange-600 rounded-2xl blur-md opacity-0 group-hover:opacity-30 transition-opacity duration-500"></div>
          <div class="relative w-16 h-16 luxury-gradient rounded-2xl flex items-center justify-center mb-6 group-hover:scale-110 transition-transform duration-300 glow-effect rotate-in">
            <i class="fas fa-dumbbell text-white text-2xl group-hover:rotate-12 transition-transform duration-300"></i>
          </div>
        </div>
        <h3 class="text-2xl font-bold mb-4 text-gray-900 group-hover:text-yellow-600 transition-colors duration-300">Fitness Center</h3>
        <p class="text-gray-600 leading-relaxed group-hover:text-gray-700 transition-colors duration-300">Fully equipped gymnasium with modern fitness equipment</p>
        <div class="mt-4 opacity-0 group-hover:opacity-100 transition-opacity duration-300">
          <div class="w-full bg-gray-200 rounded-full h-1">
            <div class="bg-gradient-to-r from-orange-400 to-orange-600 h-1 rounded-full w-0 group-hover:w-full transition-all duration-1000"></div>
          </div>
        </div>
      </div>

      <!-- Amenity Card 9 -->
      <div class="group bg-white rounded-2xl p-8 luxury-shadow hover-lift tilt-effect magnetic" data-aos="flip-left" data-aos-delay="900">
        <div class="relative">
          <div class="absolute inset-0 bg-gradient-to-r from-pink-400 to-pink-600 rounded-2xl blur-md opacity-0 group-hover:opacity-30 transition-opacity duration-500"></div>
          <div class="relative w-16 h-16 luxury-gradient rounded-2xl flex items-center justify-center mb-6 group-hover:scale-110 transition-transform duration-300 glow-effect rotate-in">
            <i class="fas fa-users text-white text-2xl group-hover:rotate-12 transition-transform duration-300"></i>
          </div>
        </div>
        <h3 class="text-2xl font-bold mb-4 text-gray-900 group-hover:text-yellow-600 transition-colors duration-300">Community Hall</h3>
        <p class="text-gray-600 leading-relaxed group-hover:text-gray-700 transition-colors duration-300">Elegant community hall for events and social gatherings</p>
        <div class="mt-4 opacity-0 group-hover:opacity-100 transition-opacity duration-300">
          <div class="w-full bg-gray-200 rounded-full h-1">
            <div class="bg-gradient-to-r from-pink-400 to-pink-600 h-1 rounded-full w-0 group-hover:w-full transition-all duration-1000"></div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Background Elements -->
  <div class="absolute top-0 right-0 w-96 h-96 bg-gradient-to-bl from-yellow-100 to-transparent rounded-full opacity-30"></div>
  <div class="absolute bottom-0 left-0 w-64 h-64 bg-gradient-to-tr from-green-100 to-transparent rounded-full opacity-30"></div>
</section>

<!-- Gallery Section -->
<section id="gallery" class="py-20 px-6 md:px-16 dark-gradient relative overflow-hidden">
  <div class="max-w-7xl mx-auto">
    <div class="text-center mb-16" data-aos="fade-up">
      <h2 class="text-5xl md:text-6xl font-playfair font-bold mb-6 text-white">
        Luxury <span class="gradient-text">Gallery</span>
      </h2>
      <div class="w-24 h-1 mx-auto mb-8" style="background: linear-gradient(135deg, #D4AF37 0%, #FFD700 50%, #B8860B 100%);"></div>
      <p class="max-w-3xl mx-auto text-xl text-gray-300 leading-relaxed">
        Explore the exquisite design and premium finishes of our luxury villas
      </p>
    </div>

    <!-- Main Gallery Slider -->
    <div class="mb-16" data-aos="fade-up" data-aos-delay="200">
      <div class="swiper gallery-main luxury-shadow rounded-2xl overflow-hidden">
        <div class="swiper-wrapper">
          <div class="swiper-slide">
            <img src="https://images.unsplash.com/photo-1600607687939-ce8a6c25118c?ixlib=rb-4.0.3&auto=format&fit=crop&w=2000&q=80"
                 alt="Luxury Villa Exterior" class="w-full h-96 md:h-[500px] object-cover">
            <div class="absolute bottom-0 left-0 right-0 bg-gradient-to-t from-black to-transparent p-8">
              <h3 class="text-2xl font-bold text-white mb-2">Villa Exterior</h3>
              <p class="text-gray-300">Contemporary architecture with premium finishes</p>
            </div>
          </div>
          <div class="swiper-slide">
            <img src="https://images.unsplash.com/photo-1600566753190-17f0baa2a6c3?ixlib=rb-4.0.3&auto=format&fit=crop&w=2000&q=80"
                 alt="Living Room" class="w-full h-96 md:h-[500px] object-cover">
            <div class="absolute bottom-0 left-0 right-0 bg-gradient-to-t from-black to-transparent p-8">
              <h3 class="text-2xl font-bold text-white mb-2">Spacious Living Room</h3>
              <p class="text-gray-300">Open-plan living with luxury interiors</p>
            </div>
          </div>
          <div class="swiper-slide">
            <img src="https://images.unsplash.com/photo-1600210492486-724fe5c67fb0?ixlib=rb-4.0.3&auto=format&fit=crop&w=2000&q=80"
                 alt="Master Bedroom" class="w-full h-96 md:h-[500px] object-cover">
            <div class="absolute bottom-0 left-0 right-0 bg-gradient-to-t from-black to-transparent p-8">
              <h3 class="text-2xl font-bold text-white mb-2">Master Bedroom</h3>
              <p class="text-gray-300">Elegant bedrooms with premium amenities</p>
            </div>
          </div>
          <div class="swiper-slide">
            <img src="https://images.unsplash.com/photo-1600566753086-00f18fb6b3ea?ixlib=rb-4.0.3&auto=format&fit=crop&w=2000&q=80"
                 alt="Modern Kitchen" class="w-full h-96 md:h-[500px] object-cover">
            <div class="absolute bottom-0 left-0 right-0 bg-gradient-to-t from-black to-transparent p-8">
              <h3 class="text-2xl font-bold text-white mb-2">Designer Kitchen</h3>
              <p class="text-gray-300">State-of-the-art kitchen with premium appliances</p>
            </div>
          </div>
          <div class="swiper-slide">
            <img src="https://images.unsplash.com/photo-1600607687644-c7171b42498b?ixlib=rb-4.0.3&auto=format&fit=crop&w=2000&q=80"
                 alt="Terrace Garden" class="w-full h-96 md:h-[500px] object-cover">
            <div class="absolute bottom-0 left-0 right-0 bg-gradient-to-t from-black to-transparent p-8">
              <h3 class="text-2xl font-bold text-white mb-2">Terrace Garden</h3>
              <p class="text-gray-300">Private terrace with panoramic green views</p>
            </div>
          </div>
        </div>
        <div class="swiper-pagination"></div>
        <div class="swiper-button-next"></div>
        <div class="swiper-button-prev"></div>
      </div>
    </div>

    <!-- Thumbnail Gallery -->
    <div class="grid grid-cols-2 md:grid-cols-4 gap-6" data-aos="fade-up" data-aos-delay="400">
      <div class="group cursor-pointer hover-lift">
        <img src="https://images.unsplash.com/photo-1600607687920-4e2a09cf159d?ixlib=rb-4.0.3&auto=format&fit=crop&w=500&q=80"
             alt="Villa Night View" class="w-full h-48 object-cover rounded-xl luxury-shadow">
        <div class="mt-4 text-center">
          <h4 class="text-lg font-semibold text-white group-hover:text-yellow-400 transition-colors">Night View</h4>
        </div>
      </div>
      <div class="group cursor-pointer hover-lift">
        <img src="https://images.unsplash.com/photo-1600607688969-a5bfcd646154?ixlib=rb-4.0.3&auto=format&fit=crop&w=500&q=80"
             alt="Community Area" class="w-full h-48 object-cover rounded-xl luxury-shadow">
        <div class="mt-4 text-center">
          <h4 class="text-lg font-semibold text-white group-hover:text-yellow-400 transition-colors">Community</h4>
        </div>
      </div>
      <div class="group cursor-pointer hover-lift">
        <img src="https://images.unsplash.com/photo-1600607688960-e095ff8d5c6e?ixlib=rb-4.0.3&auto=format&fit=crop&w=500&q=80"
             alt="Swimming Pool" class="w-full h-48 object-cover rounded-xl luxury-shadow">
        <div class="mt-4 text-center">
          <h4 class="text-lg font-semibold text-white group-hover:text-yellow-400 transition-colors">Pool Area</h4>
        </div>
      </div>
      <div class="group cursor-pointer hover-lift">
        <img src="https://images.unsplash.com/photo-1600607688888-1e4e4e8e8e8e?ixlib=rb-4.0.3&auto=format&fit=crop&w=500&q=80"
             alt="Parking Area" class="w-full h-48 object-cover rounded-xl luxury-shadow">
        <div class="mt-4 text-center">
          <h4 class="text-lg font-semibold text-white group-hover:text-yellow-400 transition-colors">Parking</h4>
        </div>
      </div>
    </div>
  </div>

  <!-- Background Elements -->
  <div class="absolute top-20 left-10 w-32 h-32 bg-yellow-400 rounded-full opacity-10 animate-pulse"></div>
  <div class="absolute bottom-20 right-10 w-24 h-24 bg-green-400 rounded-full opacity-10 animate-pulse" style="animation-delay: 2s;"></div>
</section>

<!-- Contact Section -->
<section id="contact" class="py-20 px-6 md:px-16 dark-gradient relative overflow-hidden">
  <div class="max-w-7xl mx-auto">
    <div class="text-center mb-16" data-aos="fade-up">
      <h2 class="text-5xl md:text-6xl font-playfair font-bold mb-6 text-white">
        Get In <span class="gradient-text">Touch</span>
      </h2>
      <div class="w-24 h-1 mx-auto mb-8" style="background: linear-gradient(135deg, #D4AF37 0%, #FFD700 50%, #B8860B 100%);"></div>
      <p class="max-w-3xl mx-auto text-xl text-gray-300 leading-relaxed">
        Ready to experience luxury living? Contact us today to schedule your site visit
      </p>
    </div>

    <div class="grid lg:grid-cols-2 gap-16 items-start">
      <!-- Contact Form -->
      <div data-aos="fade-right">
        <div class="bg-white rounded-2xl p-8 luxury-shadow">
          <h3 class="text-2xl font-bold mb-6 text-gray-900">Send us a Message</h3>
          <form id="contactForm" class="space-y-6">
            <div class="grid md:grid-cols-2 gap-6">
              <div>
                <label for="firstName" class="block text-sm font-semibold text-gray-700 mb-2">First Name *</label>
                <input type="text" id="firstName" name="firstName" required
                       class="w-full px-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-green-500 focus:border-transparent transition-all duration-300">
              </div>
              <div>
                <label for="lastName" class="block text-sm font-semibold text-gray-700 mb-2">Last Name *</label>
                <input type="text" id="lastName" name="lastName" required
                       class="w-full px-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-green-500 focus:border-transparent transition-all duration-300">
              </div>
            </div>

            <div>
              <label for="email" class="block text-sm font-semibold text-gray-700 mb-2">Email Address *</label>
              <input type="email" id="email" name="email" required
                     class="w-full px-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-green-500 focus:border-transparent transition-all duration-300">
            </div>

            <div>
              <label for="phone" class="block text-sm font-semibold text-gray-700 mb-2">Phone Number *</label>
              <input type="tel" id="phone" name="phone" required
                     class="w-full px-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-green-500 focus:border-transparent transition-all duration-300">
            </div>

            <div>
              <label for="interest" class="block text-sm font-semibold text-gray-700 mb-2">I'm Interested In</label>
              <select id="interest" name="interest"
                      class="w-full px-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-green-500 focus:border-transparent transition-all duration-300">
                <option value="">Select an option</option>
                <option value="site-visit">Site Visit</option>
                <option value="brochure">Download Brochure</option>
                <option value="pricing">Pricing Information</option>
                <option value="floor-plans">Floor Plans</option>
                <option value="investment">Investment Opportunity</option>
              </select>
            </div>

            <div>
              <label for="message" class="block text-sm font-semibold text-gray-700 mb-2">Message</label>
              <textarea id="message" name="message" rows="4"
                        class="w-full px-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-green-500 focus:border-transparent transition-all duration-300"
                        placeholder="Tell us about your requirements..."></textarea>
            </div>

            <button type="submit" class="luxury-btn w-full">
              <i class="fas fa-paper-plane mr-2"></i>
              Send Message
            </button>
          </form>
        </div>
      </div>

      <!-- Contact Information -->
      <div data-aos="fade-left">
        <div class="space-y-8">
          <!-- Quick Contact -->
          <div class="bg-white bg-opacity-10 backdrop-blur-lg rounded-2xl p-8 border border-white border-opacity-20">
            <h3 class="text-2xl font-bold mb-6 text-white">Quick Contact</h3>
            <div class="space-y-6">
              <div class="flex items-center">
                <div class="w-12 h-12 luxury-gradient rounded-xl flex items-center justify-center mr-4">
                  <i class="fas fa-phone text-white"></i>
                </div>
                <div>
                  <p class="text-gray-300 text-sm">Call Us</p>
                  <a href="tel:+917507007875" class="text-white text-lg font-semibold hover:text-yellow-400 transition-colors">+91 7507007875</a>
                </div>
              </div>

              <div class="flex items-center">
                <div class="w-12 h-12 luxury-gradient rounded-xl flex items-center justify-center mr-4">
                  <i class="fas fa-envelope text-white"></i>
                </div>
                <div>
                  <p class="text-gray-300 text-sm">Email Us</p>
                  <a href="mailto:<EMAIL>" class="text-white text-lg font-semibold hover:text-yellow-400 transition-colors"><EMAIL></a>
                </div>
              </div>

              <div class="flex items-center">
                <div class="w-12 h-12 luxury-gradient rounded-xl flex items-center justify-center mr-4">
                  <i class="fas fa-map-marker-alt text-white"></i>
                </div>
                <div>
                  <p class="text-gray-300 text-sm">Visit Us</p>
                  <p class="text-white text-lg font-semibold">Pimple Nilakh, Pune</p>
                </div>
              </div>
            </div>
          </div>

          <!-- WhatsApp Contact -->
          <div class="bg-gradient-to-r from-green-500 to-green-600 rounded-2xl p-8 text-white">
            <div class="flex items-center mb-4">
              <i class="fab fa-whatsapp text-3xl mr-4"></i>
              <div>
                <h3 class="text-xl font-bold">WhatsApp Us</h3>
                <p class="text-green-100">Get instant responses</p>
              </div>
            </div>
            <p class="mb-6 text-green-100">Chat with our property experts for immediate assistance and quick answers to all your queries.</p>
            <a href="https://wa.me/917020637569?text=Hi%2C%20I%27m%20interested%20in%20Green%20Alley%20luxury%20villas.%20Please%20share%20more%20details."
               target="_blank"
               class="bg-white text-green-600 px-6 py-3 rounded-xl font-semibold hover:bg-green-50 transition-colors inline-flex items-center">
              <i class="fab fa-whatsapp mr-2"></i>
              Start Chat
            </a>
          </div>

          <!-- Office Hours -->
          <div class="bg-white bg-opacity-10 backdrop-blur-lg rounded-2xl p-8 border border-white border-opacity-20">
            <h3 class="text-2xl font-bold mb-6 text-white">Office Hours</h3>
            <div class="space-y-3 text-gray-300">
              <div class="flex justify-between">
                <span>Monday - Friday</span>
                <span class="text-white font-semibold">9:00 AM - 7:00 PM</span>
              </div>
              <div class="flex justify-between">
                <span>Saturday</span>
                <span class="text-white font-semibold">9:00 AM - 6:00 PM</span>
              </div>
              <div class="flex justify-between">
                <span>Sunday</span>
                <span class="text-white font-semibold">10:00 AM - 5:00 PM</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Background Elements -->
  <div class="absolute top-0 right-0 w-96 h-96 bg-gradient-to-bl from-yellow-400 to-transparent rounded-full opacity-10"></div>
  <div class="absolute bottom-0 left-0 w-64 h-64 bg-gradient-to-tr from-green-400 to-transparent rounded-full opacity-10"></div>
</section>

<!-- CTA Section -->
<section id="enquiry" class="py-20 px-6 md:px-16 bg-white relative overflow-hidden">
  <div class="max-w-4xl mx-auto text-center">
    <div data-aos="fade-up">
      <h2 class="text-4xl md:text-5xl font-playfair font-bold mb-6 text-gray-900">
        Ready to Experience <span class="gradient-text">Luxury Living?</span>
      </h2>
      <p class="text-xl text-gray-600 mb-8 leading-relaxed">
        Your dream villa awaits. Book your exclusive site visit today and discover the Green Alley difference.
      </p>

      <div class="flex flex-col md:flex-row justify-center items-center gap-6 mb-12">
        <a href="tel:+917507007875" class="luxury-btn">
          <i class="fas fa-phone mr-2"></i>
          Call: +91 7507007875
        </a>
        <a href="https://wa.me/917020637569?text=Hi%2C%20I%27m%20interested%20in%20booking%20a%20site%20visit%20for%20Green%20Alley%20villas"
           target="_blank"
           class="bg-green-500 hover:bg-green-600 text-white px-8 py-4 rounded-full font-semibold transition-all duration-300 hover:scale-105 hover:shadow-lg">
          <i class="fab fa-whatsapp mr-2"></i>
          WhatsApp Now
        </a>

        <!-- Demo Alert Button (Remove this in production) -->
        <button onclick="showConfirmAlert()" class="luxury-btn morph-btn magnetic" style="background: linear-gradient(135deg, #3B82F6 0%, #60A5FA 50%, #2563EB 100%);">
          <i class="fas fa-bell mr-2"></i>
          Test Alert
        </button>
      </div>

      <div class="grid md:grid-cols-3 gap-8 text-center">
        <div class="p-6">
          <div class="w-16 h-16 luxury-gradient rounded-full flex items-center justify-center mx-auto mb-4">
            <i class="fas fa-calendar-check text-white text-xl"></i>
          </div>
          <h3 class="text-lg font-semibold mb-2 text-gray-900">Book Site Visit</h3>
          <p class="text-gray-600">Schedule a personalized tour</p>
        </div>
        <div class="p-6">
          <div class="w-16 h-16 luxury-gradient rounded-full flex items-center justify-center mx-auto mb-4">
            <i class="fas fa-download text-white text-xl"></i>
          </div>
          <h3 class="text-lg font-semibold mb-2 text-gray-900">Download Brochure</h3>
          <p class="text-gray-600">Get detailed project information</p>
        </div>
        <div class="p-6">
          <div class="w-16 h-16 luxury-gradient rounded-full flex items-center justify-center mx-auto mb-4">
            <i class="fas fa-handshake text-white text-xl"></i>
          </div>
          <h3 class="text-lg font-semibold mb-2 text-gray-900">Expert Consultation</h3>
          <p class="text-gray-600">Speak with our property experts</p>
        </div>
      </div>
    </div>
  </div>

  <!-- Background Elements -->
  <div class="absolute top-0 left-0 w-64 h-64 bg-gradient-to-br from-yellow-200 to-yellow-400 rounded-full opacity-10 -translate-y-32 -translate-x-32"></div>
  <div class="absolute bottom-0 right-0 w-48 h-48 bg-gradient-to-tl from-green-200 to-green-400 rounded-full opacity-10 translate-y-24 translate-x-24"></div>
</section>

<!-- Footer -->
<footer class="py-16 bg-gray-900 text-white relative overflow-hidden">
  <div class="max-w-7xl mx-auto px-6 md:px-16">
    <div class="grid md:grid-cols-4 gap-8 mb-12">
      <!-- Company Info -->
      <div class="md:col-span-2">
        <div class="text-3xl font-playfair font-bold mb-4">
          <span class="gradient-text">Green Alley</span>
        </div>
        <p class="text-gray-300 mb-6 leading-relaxed">
          Experience luxury living in the heart of Pune with our premium 4 BHK villas featuring contemporary design, green views, and world-class amenities.
        </p>
        <div class="flex space-x-4">
          <a href="#" class="w-10 h-10 bg-white bg-opacity-10 rounded-full flex items-center justify-center hover:bg-opacity-20 transition-all duration-300">
            <i class="fab fa-facebook-f text-white"></i>
          </a>
          <a href="#" class="w-10 h-10 bg-white bg-opacity-10 rounded-full flex items-center justify-center hover:bg-opacity-20 transition-all duration-300">
            <i class="fab fa-instagram text-white"></i>
          </a>
          <a href="#" class="w-10 h-10 bg-white bg-opacity-10 rounded-full flex items-center justify-center hover:bg-opacity-20 transition-all duration-300">
            <i class="fab fa-youtube text-white"></i>
          </a>
          <a href="#" class="w-10 h-10 bg-white bg-opacity-10 rounded-full flex items-center justify-center hover:bg-opacity-20 transition-all duration-300">
            <i class="fab fa-linkedin-in text-white"></i>
          </a>
        </div>
      </div>

      <!-- Quick Links -->
      <div>
        <h3 class="text-lg font-semibold mb-4 text-white">Quick Links</h3>
        <ul class="space-y-2">
          <li><a href="#home" class="text-gray-300 hover:text-yellow-400 transition-colors">Home</a></li>
          <li><a href="#about" class="text-gray-300 hover:text-yellow-400 transition-colors">About</a></li>
          <li><a href="#gallery" class="text-gray-300 hover:text-yellow-400 transition-colors">Gallery</a></li>
          <li><a href="#amenities" class="text-gray-300 hover:text-yellow-400 transition-colors">Amenities</a></li>
          <li><a href="#location" class="text-gray-300 hover:text-yellow-400 transition-colors">Location</a></li>
          <li><a href="#contact" class="text-gray-300 hover:text-yellow-400 transition-colors">Contact</a></li>
        </ul>
      </div>

      <!-- Contact Info -->
      <div>
        <h3 class="text-lg font-semibold mb-4 text-white">Contact Info</h3>
        <div class="space-y-3">
          <div class="flex items-center">
            <i class="fas fa-phone text-yellow-400 mr-3"></i>
            <a href="tel:+917507007875" class="text-gray-300 hover:text-white transition-colors">+91 7507007875</a>
          </div>
          <div class="flex items-center">
            <i class="fas fa-envelope text-yellow-400 mr-3"></i>
            <a href="mailto:<EMAIL>" class="text-gray-300 hover:text-white transition-colors"><EMAIL></a>
          </div>
          <div class="flex items-start">
            <i class="fas fa-map-marker-alt text-yellow-400 mr-3 mt-1"></i>
            <span class="text-gray-300">Pimple Nilakh, Pune, Maharashtra</span>
          </div>
        </div>
      </div>
    </div>

    <div class="border-t border-gray-700 pt-8 text-center">
      <p class="text-gray-400">© 2025 Green Alley. All rights reserved. | Developed by TechBurst Solutions</p>
    </div>
  </div>

  <!-- Background Elements -->
  <div class="absolute top-0 right-0 w-64 h-64 bg-gradient-to-bl from-yellow-400 to-transparent rounded-full opacity-5"></div>
  <div class="absolute bottom-0 left-0 w-48 h-48 bg-gradient-to-tr from-green-400 to-transparent rounded-full opacity-5"></div>
</footer>

<!-- Floating WhatsApp Button -->
<div class="fixed bottom-30 right-30 z-1000">
  <!-- Tooltip -->
  <div class="absolute bottom-full right-0 mb-2 px-3 py-2 bg-black text-white text-sm rounded-lg opacity-0 transform translate-y-2 transition-all duration-300 whitespace-nowrap whatsapp-tooltip">
    Chat with us on WhatsApp
    <div class="absolute top-full right-4 w-0 h-0 border-l-4 border-r-4 border-t-4 border-transparent border-t-black"></div>
  </div>

  <!-- Notification Badge -->
  <div class="absolute -top-1 -right-1 w-4 h-4 bg-red-500 rounded-full flex items-center justify-center">
    <span class="text-xs text-white font-bold">1</span>
  </div>

  <a href="https://wa.me/917020637569?text=Hi%2C%20I%27m%20interested%20in%20Green%20Alley%20luxury%20villas.%20Please%20share%20more%20details."
     target="_blank"
     class="floating-whatsapp magnetic"
     onmouseenter="showWhatsAppTooltip()"
     onmouseleave="hideWhatsAppTooltip()">
    <i class="fab fa-whatsapp"></i>
  </a>
</div>

<style>
.whatsapp-tooltip.show {
  opacity: 1;
  transform: translateY(0);
}
</style>

<script>
function showWhatsAppTooltip() {
  document.querySelector('.whatsapp-tooltip').classList.add('show');
}

function hideWhatsAppTooltip() {
  document.querySelector('.whatsapp-tooltip').classList.remove('show');
}

// Auto-hide notification after 5 seconds
setTimeout(() => {
  const badge = document.querySelector('.floating-whatsapp').parentElement.querySelector('.bg-red-500');
  if (badge) {
    badge.style.opacity = '0';
    badge.style.transform = 'scale(0)';
  }
}, 5000);
</script>

<!-- JavaScript Libraries -->
<script src="https://unpkg.com/swiper@8/swiper-bundle.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/aos/2.3.4/aos.js"></script>

<script>
// Initialize AOS (Animate On Scroll)
AOS.init({
  duration: 1000,
  easing: 'ease-in-out-cubic',
  once: true,
  offset: 100,
  delay: 100
});

// Initialize Swiper for Gallery with mobile optimizations
const gallerySwiper = new Swiper('.gallery-main', {
  loop: true,
  autoplay: {
    delay: isMobile ? 4000 : 5000,
    disableOnInteraction: false,
  },
  pagination: {
    el: '.swiper-pagination',
    clickable: true,
  },
  navigation: {
    nextEl: '.swiper-button-next',
    prevEl: '.swiper-button-prev',
  },
  effect: 'fade',
  fadeEffect: {
    crossFade: true
  },
  speed: isMobile ? 600 : 1000,
  touchRatio: 1,
  touchAngle: 45,
  grabCursor: true,
  // Mobile-specific settings
  breakpoints: {
    320: {
      slidesPerView: 1,
      spaceBetween: 10
    },
    768: {
      slidesPerView: 1,
      spaceBetween: 20
    },
    1024: {
      slidesPerView: 1,
      spaceBetween: 30
    }
  }
});

// Enhanced Mobile Menu Toggle
const mobileMenuBtn = document.getElementById('mobile-menu-btn');
const mobileMenu = document.getElementById('mobile-menu');
const hamburgerMenu = mobileMenuBtn.querySelector('.hamburger-menu');

mobileMenuBtn.addEventListener('click', (e) => {
  e.preventDefault();
  e.stopPropagation();

  mobileMenu.classList.toggle('hidden');
  mobileMenu.classList.toggle('translate-y-full');
  hamburgerMenu.classList.toggle('active');

  // Prevent body scroll when menu is open
  if (!mobileMenu.classList.contains('hidden')) {
    document.body.style.overflow = 'hidden';
  } else {
    document.body.style.overflow = 'auto';
  }
});

// Close mobile menu when clicking outside
document.addEventListener('click', (e) => {
  if (!mobileMenuBtn.contains(e.target) && !mobileMenu.contains(e.target)) {
    mobileMenu.classList.add('hidden');
    mobileMenu.classList.add('translate-y-full');
    hamburgerMenu.classList.remove('active');
    document.body.style.overflow = 'auto';
  }
});

// Close mobile menu on window resize
window.addEventListener('resize', () => {
  if (window.innerWidth > 768) {
    mobileMenu.classList.add('hidden');
    mobileMenu.classList.add('translate-y-full');
    hamburgerMenu.classList.remove('active');
    document.body.style.overflow = 'auto';
  }
});

// Enhanced Navbar Scroll Effect
window.addEventListener('scroll', () => {
  const navbar = document.getElementById('navbar');
  const scrollY = window.scrollY;

  if (scrollY > 100) {
    navbar.classList.add('bg-opacity-95', 'backdrop-blur-xl');
    navbar.style.transform = 'translateY(0)';
  } else {
    navbar.classList.remove('bg-opacity-95', 'backdrop-blur-xl');
  }

  // Hide navbar on scroll down, show on scroll up
  if (scrollY > 200) {
    if (scrollY > window.lastScrollY) {
      navbar.style.transform = 'translateY(-100%)';
    } else {
      navbar.style.transform = 'translateY(0)';
    }
  }
  window.lastScrollY = scrollY;
});

// Smooth Scrolling for Navigation Links
document.querySelectorAll('a[href^="#"]').forEach(anchor => {
  anchor.addEventListener('click', function (e) {
    e.preventDefault();
    const target = document.querySelector(this.getAttribute('href'));
    if (target) {
      target.scrollIntoView({
        behavior: 'smooth',
        block: 'start'
      });
      // Close mobile menu if open
      mobileMenu.classList.add('hidden');
      mobileMenu.classList.add('translate-y-full');
      hamburgerMenu.classList.remove('active');
    }
  });
});

// Counter Animation
function animateCounters() {
  const counters = document.querySelectorAll('.counter');
  counters.forEach(counter => {
    const target = parseInt(counter.getAttribute('data-target'));
    const increment = target / 100;
    let current = 0;

    const updateCounter = () => {
      if (current < target) {
        current += increment;
        counter.textContent = Math.ceil(current);
        requestAnimationFrame(updateCounter);
      } else {
        counter.textContent = target;
      }
    };

    updateCounter();
  });
}

// Progress Bar Animation
function animateProgressBars() {
  const progressBars = document.querySelectorAll('.progress-bar');
  progressBars.forEach(bar => {
    const width = bar.getAttribute('data-width');
    setTimeout(() => {
      bar.style.width = width + '%';
    }, 500);
  });
}

// Intersection Observer for animations
const observerOptions = {
  threshold: 0.3,
  rootMargin: '0px 0px -50px 0px'
};

const observer = new IntersectionObserver((entries) => {
  entries.forEach(entry => {
    if (entry.isIntersecting) {
      // Animate counters
      if (entry.target.querySelector('.counter')) {
        animateCounters();
      }

      // Animate progress bars
      if (entry.target.querySelector('.progress-bar')) {
        animateProgressBars();
      }

      // Add fade-in-up animation
      entry.target.classList.add('fade-in-up');
    }
  });
}, observerOptions);

// Observe sections for animation
document.querySelectorAll('section').forEach(section => {
  observer.observe(section);
});

// Magnetic Effect for buttons and interactive elements
document.querySelectorAll('.magnetic').forEach(element => {
  element.addEventListener('mousemove', (e) => {
    const rect = element.getBoundingClientRect();
    const x = e.clientX - rect.left - rect.width / 2;
    const y = e.clientY - rect.top - rect.height / 2;

    element.style.transform = `translate(${x * 0.1}px, ${y * 0.1}px) scale(1.05)`;
  });

  element.addEventListener('mouseleave', () => {
    element.style.transform = 'translate(0px, 0px) scale(1)';
  });
});

// Parallax Effect for Hero Section
window.addEventListener('scroll', () => {
  const scrolled = window.pageYOffset;
  const parallaxElements = document.querySelectorAll('.parallax');

  parallaxElements.forEach(element => {
    const speed = element.dataset.speed || 0.5;
    const yPos = -(scrolled * speed);
    element.style.transform = `translateY(${yPos}px)`;
  });
});

// Tilt Effect
document.querySelectorAll('.tilt-effect').forEach(element => {
  element.addEventListener('mousemove', (e) => {
    const rect = element.getBoundingClientRect();
    const x = e.clientX - rect.left;
    const y = e.clientY - rect.top;

    const centerX = rect.width / 2;
    const centerY = rect.height / 2;

    const rotateX = (y - centerY) / 10;
    const rotateY = (centerX - x) / 10;

    element.style.transform = `perspective(1000px) rotateX(${rotateX}deg) rotateY(${rotateY}deg) scale3d(1.05, 1.05, 1.05)`;
  });

  element.addEventListener('mouseleave', () => {
    element.style.transform = 'perspective(1000px) rotateX(0deg) rotateY(0deg) scale3d(1, 1, 1)';
  });
});



// Custom Alert Box Functionality
class CustomAlert {
  constructor() {
    this.overlay = document.getElementById('customAlertOverlay');
    this.box = document.getElementById('customAlertBox');
    this.icon = document.getElementById('customAlertIcon');
    this.iconSymbol = document.getElementById('customAlertIconSymbol');
    this.title = document.getElementById('customAlertTitle');
    this.message = document.getElementById('customAlertMessage');
    this.buttons = document.getElementById('customAlertButtons');
    this.okBtn = document.getElementById('customAlertOkBtn');

    this.setupEventListeners();
  }

  setupEventListeners() {
    this.okBtn.addEventListener('click', () => this.hide());
    this.overlay.addEventListener('click', (e) => {
      if (e.target === this.overlay) this.hide();
    });

    // ESC key to close
    document.addEventListener('keydown', (e) => {
      if (e.key === 'Escape' && this.overlay.classList.contains('show')) {
        this.hide();
      }
    });
  }

  show(options = {}) {
    const {
      type = 'success',
      title = 'Alert',
      message = 'This is an alert message.',
      buttons = ['OK'],
      callback = null
    } = options;

    // Set alert type
    this.overlay.className = `custom-alert-overlay alert-${type}`;

    // Set icon based on type
    const icons = {
      success: 'fas fa-check',
      error: 'fas fa-times',
      warning: 'fas fa-exclamation-triangle',
      info: 'fas fa-info-circle'
    };

    this.iconSymbol.className = icons[type] || icons.success;

    // Set content
    this.title.textContent = title;
    this.message.textContent = message;

    // Create buttons
    this.buttons.innerHTML = '';
    buttons.forEach((buttonText, index) => {
      const button = document.createElement('button');
      button.className = `custom-alert-btn ${index === 0 ? 'custom-alert-btn-primary' : 'custom-alert-btn-secondary'}`;
      button.textContent = buttonText;
      button.addEventListener('click', () => {
        if (callback) callback(index, buttonText);
        this.hide();
      });
      this.buttons.appendChild(button);
    });

    // Show alert with enhanced animations
    this.overlay.classList.add('show');
    this.box.classList.add('animate-in');

    // Add floating particles
    this.addFloatingParticles();

    // Add shimmer effect
    setTimeout(() => {
      this.box.classList.add('shimmer');
    }, 500);

    // Remove shimmer effect
    setTimeout(() => {
      this.box.classList.remove('shimmer');
    }, 2500);

    // Remove entrance animation class
    setTimeout(() => {
      this.box.classList.remove('animate-in');
    }, 600);
  }

  hide() {
    this.box.classList.add('animate-out');

    setTimeout(() => {
      this.overlay.classList.remove('show');
      this.box.classList.remove('shimmer', 'animate-out');
      this.removeFloatingParticles();
    }, 300);
  }

  addFloatingParticles() {
    // Remove existing particles
    this.removeFloatingParticles();

    // Add new particles
    for (let i = 0; i < 6; i++) {
      const particle = document.createElement('div');
      particle.className = 'alert-particle';
      particle.style.left = Math.random() * 100 + '%';
      particle.style.top = Math.random() * 100 + '%';
      particle.style.animationDelay = Math.random() * 3 + 's';
      this.box.appendChild(particle);
    }
  }

  removeFloatingParticles() {
    const particles = this.box.querySelectorAll('.alert-particle');
    particles.forEach(particle => particle.remove());
  }

  // Static methods for easy use
  static success(message, title = 'Success!', callback = null) {
    window.customAlert.show({
      type: 'success',
      title,
      message,
      callback
    });
  }

  static error(message, title = 'Error!', callback = null) {
    window.customAlert.show({
      type: 'error',
      title,
      message,
      callback
    });
  }

  static warning(message, title = 'Warning!', callback = null) {
    window.customAlert.show({
      type: 'warning',
      title,
      message,
      callback
    });
  }

  static info(message, title = 'Information', callback = null) {
    window.customAlert.show({
      type: 'info',
      title,
      message,
      callback
    });
  }

  static confirm(message, title = 'Confirm', callback = null) {
    window.customAlert.show({
      type: 'warning',
      title,
      message,
      buttons: ['Yes', 'No'],
      callback
    });
  }
}

// Initialize custom alert
window.customAlert = new CustomAlert();

// Override default alert
window.alert = function(message) {
  CustomAlert.info(message);
};























// Mobile Device Detection and Optimizations
const isMobile = /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);
const isTouchDevice = 'ontouchstart' in window || navigator.maxTouchPoints > 0;
const isDesktop = !isMobile && !isTouchDevice;

// Mobile optimizations (keeping mobile detection for other features)
if (isMobile) {
  // Disable particle effects on mobile for performance
  document.querySelectorAll('.particle').forEach(particle => {
    particle.style.display = 'none';
  });

  // Reduce animation complexity on mobile
  document.documentElement.style.setProperty('--animation-duration', '0.3s');
}

// Mobile-specific optimizations
if (isMobile) {
  // Reduce AOS animation duration for mobile
  AOS.init({
    duration: 600,
    easing: 'ease-in-out',
    once: true,
    offset: 50,
    delay: 50
  });

  // Optimize scroll performance on mobile
  let ticking = false;

  function updateScrollProgress() {
    const scrollTop = window.pageYOffset;
    const docHeight = document.body.scrollHeight - window.innerHeight;
    const scrollPercent = (scrollTop / docHeight) * 100;

    scrollProgress.style.width = scrollPercent + '%';
    ticking = false;
  }

  // Override scroll event for mobile
  window.removeEventListener('scroll', () => {});
  window.addEventListener('scroll', () => {
    if (!ticking) {
      requestAnimationFrame(updateScrollProgress);
      ticking = true;
    }
  }, { passive: true });

  // Disable hover effects on mobile
  document.querySelectorAll('.hover-lift, .tilt-effect, .magnetic').forEach(element => {
    element.style.transition = 'none';
  });
}

// Demo functions for testing different alert types (you can remove these)
function showSuccessAlert() {
  CustomAlert.success('Your action was completed successfully!', 'Success!');
}

function showErrorAlert() {
  CustomAlert.error('Something went wrong. Please try again.', 'Error!');
}

function showWarningAlert() {
  CustomAlert.warning('Please review your information before proceeding.', 'Warning!');
}

function showInfoAlert() {
  CustomAlert.info('Here is some important information for you.', 'Information');
}

function showConfirmAlert() {
  CustomAlert.confirm('Are you sure you want to proceed?', 'Confirm Action', (buttonIndex, buttonText) => {
    if (buttonIndex === 0) {
      CustomAlert.success('Action confirmed!');
    } else {
      CustomAlert.info('Action cancelled.');
    }
  });
}

// Enhanced scrollbar interaction and scroll progress
let isScrolling = false;
const scrollProgress = document.getElementById('scrollProgress');

window.addEventListener('scroll', () => {
  // Update scroll progress
  const scrollTop = window.pageYOffset;
  const docHeight = document.body.scrollHeight - window.innerHeight;
  const scrollPercent = (scrollTop / docHeight) * 100;

  scrollProgress.style.width = scrollPercent + '%';

  // Dynamic scroll progress color based on scroll position
  if (scrollPercent < 25) {
    scrollProgress.style.background = 'linear-gradient(to right, #2d5016, #4a7c59)';
  } else if (scrollPercent < 50) {
    scrollProgress.style.background = 'linear-gradient(to right, #4a7c59, #2d5016, #6b8e23)';
  } else if (scrollPercent < 75) {
    scrollProgress.style.background = 'linear-gradient(to right, #2d5016, #4a7c59, #8B4513)';
  } else {
    scrollProgress.style.background = 'linear-gradient(to right, #4a7c59, #2d5016, #8B4513, #6b8e23)';
  }
});

// Contact Form Handling
const contactForm = document.getElementById('contactForm');
contactForm.addEventListener('submit', function(e) {
  e.preventDefault();

  // Get form data
  const formData = new FormData(contactForm);
  const firstName = formData.get('firstName');
  const lastName = formData.get('lastName');
  const email = formData.get('email');
  const phone = formData.get('phone');
  const interest = formData.get('interest');
  const message = formData.get('message');

  // Create mailto link
  const subject = `Green Alley Inquiry from ${firstName} ${lastName}`;
  const body = `
Name: ${firstName} ${lastName}
Email: ${email}
Phone: ${phone}
Interest: ${interest}
Message: ${message}

Best regards,
${firstName} ${lastName}
  `;

  const mailtoLink = `mailto:<EMAIL>?subject=${encodeURIComponent(subject)}&body=${encodeURIComponent(body)}`;

  // Open email client
  window.location.href = mailtoLink;

  // Show success message with custom alert
  CustomAlert.success(
    'Your email client will open with a pre-filled message. Please send it to complete your inquiry.',
    'Thank You for Your Inquiry!',
    () => {
      // Reset form after user acknowledges
      contactForm.reset();
    }
  );
});

// Mobile-specific form enhancements
if (isMobile) {
  // Add touch-friendly focus effects
  const formInputs = document.querySelectorAll('#contactForm input, #contactForm textarea, #contactForm select');

  formInputs.forEach(input => {
    input.addEventListener('focus', () => {
      input.style.transform = 'scale(1.02)';
      input.style.boxShadow = '0 5px 15px rgba(212, 175, 55, 0.2)';
    });

    input.addEventListener('blur', () => {
      input.style.transform = 'scale(1)';
      input.style.boxShadow = 'none';
    });
  });

  // Add form validation feedback
  const submitBtn = contactForm.querySelector('button[type="submit"]');
  submitBtn.addEventListener('click', (e) => {
    const requiredFields = contactForm.querySelectorAll('[required]');
    let isValid = true;

    requiredFields.forEach(field => {
      if (!field.value.trim()) {
        field.style.borderColor = '#EF4444';
        field.style.backgroundColor = 'rgba(239, 68, 68, 0.1)';
        isValid = false;

        setTimeout(() => {
          field.style.borderColor = '';
          field.style.backgroundColor = '';
        }, 3000);
      }
    });

    if (!isValid) {
      e.preventDefault();
      CustomAlert.warning('Please fill in all required fields.', 'Missing Information');
    }
  });
}

// Parallax Effect for Hero Section
window.addEventListener('scroll', () => {
  const scrolled = window.pageYOffset;
  const parallaxElements = document.querySelectorAll('.parallax');

  parallaxElements.forEach(element => {
    const speed = element.dataset.speed || 0.5;
    const yPos = -(scrolled * speed);
    element.style.transform = `translateY(${yPos}px)`;
  });
});

// Add loading animation and mobile optimizations
window.addEventListener('load', () => {
  document.body.classList.add('loaded');

  // Mobile performance optimizations
  if (isMobile) {
    // Reduce animation complexity after load
    setTimeout(() => {
      document.querySelectorAll('.glow-effect').forEach(el => {
        el.style.animation = 'none';
      });

      // Optimize images for mobile
      document.querySelectorAll('img').forEach(img => {
        img.loading = 'lazy';
        img.style.willChange = 'auto';
      });
    }, 3000);
  }
});

// Mobile-specific touch optimizations
if (isMobile) {
  // Prevent zoom on double tap for buttons
  document.querySelectorAll('.luxury-btn, button').forEach(btn => {
    btn.addEventListener('touchend', (e) => {
      e.preventDefault();
      btn.click();
    });
  });

  // Optimize scroll performance
  document.addEventListener('touchstart', () => {}, { passive: true });
  document.addEventListener('touchmove', () => {}, { passive: true });
}

// Observe hover-lift elements for animation
document.querySelectorAll('.hover-lift').forEach(el => {
  observer.observe(el);
});
</script>

</body>
</html>
