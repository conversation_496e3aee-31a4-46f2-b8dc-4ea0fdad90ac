<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />

  <!-- Primary Meta Tags -->
  <title>Green Alley | Luxury 4 BHK Row Houses in Pimple Nilakh, Pune | Army Green Zone Views</title>
  <meta name="title" content="Green Alley | Luxury 4 BHK Row Houses in Pimple Nilakh, Pune | Army Green Zone Views" />
  <meta name="description" content="Discover Green Alley - Premium 4 BHK row houses in Pimple Nilakh, Pune with guaranteed army green zone views, individual terrace provisions for swimming pool & gym. Book site visit today!" />
  <meta name="keywords" content="Green Alley, 4 BHK row houses Pimple Nilakh, luxury villas Pune, army green zone views, row houses for sale Pune, Pimple Nilakh properties, swimming pool provision, gym provision, nature facing homes Pune" />
  <meta name="author" content="Green Alley" />
  <meta name="robots" content="index, follow" />
  <meta name="language" content="English" />
  <meta name="revisit-after" content="7 days" />

  <!-- Open Graph / Facebook -->
  <meta property="og:type" content="website" />
  <meta property="og:url" content="https://greenalley.in/" />
  <meta property="og:title" content="Green Alley | Luxury 4 BHK Row Houses in Pimple Nilakh, Pune" />
  <meta property="og:description" content="Premium 4 BHK row houses with army green zone views, individual terrace provisions for swimming pool & gym in Pimple Nilakh, Pune." />
  <meta property="og:image" content="https://greenalley.in/img/villa-hero.jpeg" />
  <meta property="og:image:width" content="1200" />
  <meta property="og:image:height" content="630" />
  <meta property="og:site_name" content="Green Alley" />
  <meta property="og:locale" content="en_IN" />

  <!-- Twitter -->
  <meta property="twitter:card" content="summary_large_image" />
  <meta property="twitter:url" content="https://greenalley.in/" />
  <meta property="twitter:title" content="Green Alley | Luxury 4 BHK Row Houses in Pimple Nilakh, Pune" />
  <meta property="twitter:description" content="Premium 4 BHK row houses with army green zone views, individual terrace provisions for swimming pool & gym in Pimple Nilakh, Pune." />
  <meta property="twitter:image" content="https://greenalley.in/img/villa-hero.jpeg" />

  <!-- Additional SEO Meta Tags -->
  <meta name="geo.region" content="IN-MH" />
  <meta name="geo.placename" content="Pimple Nilakh, Pune" />
  <meta name="geo.position" content="18.5669;73.7855" />
  <meta name="ICBM" content="18.5669, 73.7855" />

  <!-- Canonical URL -->
  <link rel="canonical" href="https://greenalley.in/" />

  <!-- Performance Optimization -->
  <link rel="dns-prefetch" href="//fonts.googleapis.com">
  <link rel="dns-prefetch" href="//fonts.gstatic.com">
  <link rel="dns-prefetch" href="//cdn.jsdelivr.net">
  <link rel="dns-prefetch" href="//unpkg.com">
  <link rel="dns-prefetch" href="//cdnjs.cloudflare.com">

  <!-- Preload Critical Resources -->
  <link rel="preload" href="img/villa-hero.jpeg" as="image" type="image/jpeg">
  <link rel="preload" href="https://fonts.googleapis.com/css2?family=Playfair+Display:wght@400;500;600;700;800;900&family=Inter:wght@300;400;500;600;700&display=swap" as="style">

  <!-- Fonts -->
  <link rel="preconnect" href="https://fonts.googleapis.com">
  <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
  <link href="https://fonts.googleapis.com/css2?family=Playfair+Display:wght@400;500;600;700;800;900&family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">

  <!-- CSS Libraries -->
  <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
  <link rel="stylesheet" href="https://unpkg.com/swiper@8/swiper-bundle.min.css" />
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/aos/2.3.4/aos.css" />
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" />

  <!-- Custom Styles -->
  <style>
    :root {
      --primary-gold: #D4AF37;
      --primary-dark: #1a1a1a;
      --primary-green: #2d5016;
      --luxury-gradient: linear-gradient(135deg, #D4AF37 0%, #FFD700 50%, #B8860B 100%);
      --dark-gradient: linear-gradient(135deg, #1a1a1a 0%, #2d2d2d 100%);
      --vh: 1vh;
    }

    * {
      scroll-behavior: smooth;
    }

    body {
      font-family: 'Inter', sans-serif;
      overflow-x: hidden;
      -webkit-font-smoothing: antialiased;
      -moz-osx-font-smoothing: grayscale;
    }

    /* Mobile viewport fix */
    .mobile-vh {
      height: calc(var(--vh, 1vh) * 100);
    }

    .font-playfair {
      font-family: 'Playfair Display', serif;
    }

    .luxury-gradient {
      background: linear-gradient(135deg, #D4AF37 0%, #FFD700 50%, #B8860B 100%);
    }

    .dark-gradient {
      background: linear-gradient(135deg, #1a1a1a 0%, #2d2d2d 100%);
    }

    /* Fix for gradient text */
    .luxury-gradient.bg-clip-text {
      background: linear-gradient(135deg, #D4AF37 0%, #FFD700 50%, #B8860B 100%);
      -webkit-background-clip: text;
      background-clip: text;
      -webkit-text-fill-color: transparent;
      color: transparent;
    }

    .glass-effect {
      background: rgba(255, 255, 255, 0.1);
      backdrop-filter: blur(10px);
      border: 1px solid rgba(255, 255, 255, 0.2);
    }

    .luxury-shadow {
      box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25), 0 0 0 1px rgba(255, 255, 255, 0.05);
    }

    .hover-lift {
      transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
    }

    .hover-lift:hover {
      transform: translateY(-10px) scale(1.02);
    }

    .parallax {
      transform: translateZ(0);
      will-change: transform;
    }

    .luxury-btn {
      background: linear-gradient(135deg, #D4AF37 0%, #FFD700 50%, #B8860B 100%);
      color: white;
      padding: 16px 32px;
      border-radius: 50px;
      font-weight: 600;
      text-transform: uppercase;
      letter-spacing: 1px;
      transition: all 0.3s ease;
      position: relative;
      overflow: hidden;
      border: none;
      cursor: pointer;
      display: inline-flex;
      align-items: center;
      justify-content: center;
      text-decoration: none;
    }

    .luxury-btn::before {
      content: '';
      position: absolute;
      top: 0;
      left: -100%;
      width: 100%;
      height: 100%;
      background: linear-gradient(90deg, transparent, rgba(255,255,255,0.3), transparent);
      transition: left 0.5s;
    }

    .luxury-btn:hover::before {
      left: 100%;
    }

    .luxury-btn:hover {
      transform: translateY(-2px);
      box-shadow: 0 15px 35px rgba(212, 175, 55, 0.4);
      background: linear-gradient(135deg, #FFD700 0%, #D4AF37 50%, #B8860B 100%);
    }

    .floating-whatsapp {
      position: fixed;
      bottom: 30px;
      right: 30px;
      z-index: 1000;
      background: #25D366;
      color: white;
      width: 60px;
      height: 60px;
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 24px;
      box-shadow: 0 8px 25px rgba(37, 211, 102, 0.4);
      animation: pulse 2s infinite;
      transition: all 0.3s ease;
    }

    .floating-whatsapp:hover {
      transform: scale(1.1);
      box-shadow: 0 12px 35px rgba(37, 211, 102, 0.6);
    }

    @keyframes pulse {
      0% { box-shadow: 0 0 0 0 rgba(37, 211, 102, 0.7); }
      70% { box-shadow: 0 0 0 10px rgba(37, 211, 102, 0); }
      100% { box-shadow: 0 0 0 0 rgba(37, 211, 102, 0); }
    }

    @keyframes fadeInUp {
      from {
        opacity: 0;
        transform: translateY(30px);
      }
      to {
        opacity: 1;
        transform: translateY(0);
      }
    }

    .fade-in-up {
      animation: fadeInUp 0.8s ease-out;
    }

    .navbar-glass {
      background: rgba(26, 26, 26, 0.9);
      backdrop-filter: blur(20px);
      border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    }

    .hero-overlay {
      background: linear-gradient(135deg, rgba(26, 26, 26, 0.7) 0%, rgba(45, 80, 22, 0.8) 100%);
    }

    /* Custom Scrollbar */
    ::-webkit-scrollbar {
      width: 12px;
    }

    ::-webkit-scrollbar-track {
      background: var(--primary-dark);
      border-radius: 10px;
    }

    ::-webkit-scrollbar-thumb {
      background: var(--luxury-gradient);
      border-radius: 10px;
      border: 2px solid var(--primary-dark);
      box-shadow: 0 2px 10px rgba(212, 175, 55, 0.3);
    }

    ::-webkit-scrollbar-thumb:hover {
      background: linear-gradient(135deg, #FFD700 0%, #D4AF37 50%, #B8860B 100%);
      box-shadow: 0 4px 20px rgba(212, 175, 55, 0.5);
    }

    /* Custom Alert Box */
    .custom-alert-overlay {
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background: rgba(0, 0, 0, 0.8);
      backdrop-filter: blur(5px);
      z-index: 10000;
      display: flex;
      align-items: center;
      justify-content: center;
      opacity: 0;
      visibility: hidden;
      transition: all 0.3s ease;
    }

    .custom-alert-overlay.show {
      opacity: 1;
      visibility: visible;
    }

    .custom-alert-box {
      background: linear-gradient(135deg, #1a1a1a 0%, #2d2d2d 100%);
      border: 2px solid rgba(212, 175, 55, 0.3);
      border-radius: 20px;
      padding: 30px;
      max-width: 500px;
      width: 90%;
      box-shadow: 0 20px 60px rgba(0, 0, 0, 0.5);
      position: relative;
      transform: scale(0.7) translateY(-50px);
      transition: all 0.3s cubic-bezier(0.34, 1.56, 0.64, 1);
      overflow: hidden;
    }

    .custom-alert-overlay.show .custom-alert-box {
      transform: scale(1) translateY(0);
    }

    .custom-alert-box::before {
      content: '';
      position: absolute;
      top: 0;
      left: -100%;
      width: 100%;
      height: 100%;
      background: linear-gradient(90deg, transparent, rgba(212, 175, 55, 0.1), transparent);
      transition: left 2s ease;
    }

    .custom-alert-box.shimmer::before {
      left: 100%;
    }

    .custom-alert-icon {
      width: 60px;
      height: 60px;
      border-radius: 50%;
      background: var(--luxury-gradient);
      margin: 0 auto 20px;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 24px;
      color: white;
      box-shadow: 0 10px 25px rgba(212, 175, 55, 0.3);
      animation: pulse-glow 2s infinite;
    }

    @keyframes pulse-glow {
      0%, 100% {
        box-shadow: 0 10px 25px rgba(212, 175, 55, 0.3);
        transform: scale(1);
      }
      50% {
        box-shadow: 0 15px 35px rgba(212, 175, 55, 0.5);
        transform: scale(1.05);
      }
    }

    .custom-alert-title {
      color: white;
      font-size: 24px;
      font-weight: bold;
      text-align: center;
      margin-bottom: 15px;
      font-family: 'Playfair Display', serif;
    }

    .custom-alert-message {
      color: #e5e5e5;
      font-size: 16px;
      line-height: 1.6;
      text-align: center;
      margin-bottom: 25px;
    }

    .custom-alert-buttons {
      display: flex;
      gap: 15px;
      justify-content: center;
    }

    .custom-alert-btn {
      padding: 12px 30px;
      border: none;
      border-radius: 25px;
      font-weight: 600;
      font-size: 14px;
      cursor: pointer;
      transition: all 0.3s ease;
      position: relative;
      overflow: hidden;
      text-transform: uppercase;
      letter-spacing: 1px;
    }

    .custom-alert-btn-primary {
      background: var(--luxury-gradient);
      color: white;
      box-shadow: 0 5px 15px rgba(212, 175, 55, 0.3);
    }

    .custom-alert-btn-primary:hover {
      transform: translateY(-2px);
      box-shadow: 0 8px 25px rgba(212, 175, 55, 0.4);
    }

    .custom-alert-btn-secondary {
      background: rgba(255, 255, 255, 0.1);
      color: white;
      border: 1px solid rgba(255, 255, 255, 0.2);
    }

    .custom-alert-btn-secondary:hover {
      background: rgba(255, 255, 255, 0.2);
      transform: translateY(-2px);
    }

    .custom-alert-btn::before {
      content: '';
      position: absolute;
      top: 0;
      left: -100%;
      width: 100%;
      height: 100%;
      background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
      transition: left 0.5s;
    }

    .custom-alert-btn:hover::before {
      left: 100%;
    }

    /* Alert types */
    .alert-success .custom-alert-icon {
      background: linear-gradient(135deg, #10B981 0%, #34D399 50%, #059669 100%);
    }

    .alert-error .custom-alert-icon {
      background: linear-gradient(135deg, #EF4444 0%, #F87171 50%, #DC2626 100%);
    }

    .alert-warning .custom-alert-icon {
      background: linear-gradient(135deg, #F59E0B 0%, #FBBF24 50%, #D97706 100%);
    }

    .alert-info .custom-alert-icon {
      background: linear-gradient(135deg, #3B82F6 0%, #60A5FA 50%, #2563EB 100%);
    }

    /* Lightbox Styles */
    #lightbox {
      backdrop-filter: blur(10px);
      animation: fadeIn 0.3s ease;
    }

    #lightbox img {
      box-shadow: 0 25px 50px rgba(0, 0, 0, 0.5);
      transition: transform 0.3s ease;
    }

    #lightbox-close {
      background: rgba(0, 0, 0, 0.5);
      border-radius: 50%;
      width: 50px;
      height: 50px;
      display: flex;
      align-items: center;
      justify-content: center;
      transition: all 0.3s ease;
    }

    #lightbox-close:hover {
      background: rgba(212, 175, 55, 0.8);
      transform: scale(1.1);
    }

    #lightbox-caption {
      background: linear-gradient(to top, rgba(0, 0, 0, 0.8), transparent);
      padding: 20px;
      border-radius: 0 0 8px 8px;
      backdrop-filter: blur(10px);
    }

    @keyframes fadeIn {
      from { opacity: 0; }
      to { opacity: 1; }
    }

    /* Image hover effects */
    .gallery-image-hover {
      position: relative;
      overflow: hidden;
    }

    .gallery-image-hover::after {
      content: '🔍';
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      font-size: 2rem;
      color: white;
      background: rgba(212, 175, 55, 0.9);
      width: 60px;
      height: 60px;
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      opacity: 0;
      transition: all 0.3s ease;
      pointer-events: none;
    }

    .gallery-image-hover:hover::after {
      opacity: 1;
      transform: translate(-50%, -50%) scale(1.1);
    }

    /* Force image visibility */
    img {
      opacity: 1 !important;
      visibility: visible !important;
      display: block !important;
      max-width: 100%;
      height: auto;
    }

    /* Swiper specific image fixes */
    .swiper-slide img {
      opacity: 1 !important;
      visibility: visible !important;
      display: block !important;
      width: 100%;
      height: 100%;
      object-fit: cover;
    }

    /* Ensure images load properly */
    .swiper-slide {
      opacity: 1 !important;
      visibility: visible !important;
    }

    .swiper-wrapper {
      opacity: 1 !important;
      visibility: visible !important;
    }

    /* Gallery main container */
    .gallery-main {
      opacity: 1 !important;
      visibility: visible !important;
    }

    /* Force all gallery images to be visible */
    .gallery-main img,
    .swiper img,
    .swiper-slide img {
      opacity: 1 !important;
      visibility: visible !important;
      display: block !important;
    }

    /* Override any fade effects that might hide images */
    .swiper-slide-active,
    .swiper-slide-next,
    .swiper-slide-prev {
      opacity: 1 !important;
    }

    /* Logo Styles */
    .navbar-logo {
      transition: all 0.3s ease;
    }

    .navbar-logo:hover {
      transform: scale(1.05);
    }

    /* Mobile Responsive Styles */
    @media (max-width: 768px) {
      /* Navigation Mobile Optimizations */
      .navbar-glass {
        padding: 0 1rem;
      }

      .navbar-glass .flex {
        height: 70px;
      }

      /* Logo responsive sizing */
      .navbar-logo {
        height: 2.5rem !important; /* 40px */
      }

      /* Hero Section Mobile */
      #home h1 {
        font-size: 2.5rem !important;
        line-height: 1.2;
        margin-bottom: 1rem;
      }

      #home p {
        font-size: 1.1rem !important;
        margin-bottom: 2rem;
        padding: 0 1rem;
      }

      #home .flex-col {
        gap: 1rem;
      }

      #home .luxury-btn {
        width: 100%;
        max-width: 280px;
        padding: 14px 24px;
        font-size: 0.9rem;
      }

      /* About Section Mobile */
      #about .grid {
        grid-template-columns: 1fr;
        gap: 2rem;
      }

      #about h2 {
        font-size: 2rem !important;
      }

      #about p {
        font-size: 1rem;
        line-height: 1.6;
      }

      /* Gallery Mobile */
      #gallery .grid {
        grid-template-columns: 1fr;
        gap: 1rem;
      }

      #gallery h2 {
        font-size: 2rem !important;
      }

      /* Amenities Mobile */
      #amenities .grid {
        grid-template-columns: repeat(2, 1fr);
        gap: 1rem;
      }

      #amenities h2 {
        font-size: 2rem !important;
      }

      #amenities .amenity-item {
        padding: 1rem;
      }

      #amenities .amenity-item h3 {
        font-size: 1rem;
      }

      #amenities .amenity-item p {
        font-size: 0.85rem;
      }

      /* Location Mobile */
      #location h2 {
        font-size: 2rem !important;
      }

      #location .grid {
        grid-template-columns: 1fr;
        gap: 2rem;
      }

      /* Contact Form Mobile */
      #contact h2 {
        font-size: 2rem !important;
      }

      #contact .grid {
        grid-template-columns: 1fr;
        gap: 2rem;
      }

      #contact form {
        padding: 1.5rem;
      }

      #contact input,
      #contact textarea,
      #contact select {
        padding: 12px 16px;
        font-size: 16px; /* Prevents zoom on iOS */
      }

      #contact .luxury-btn {
        width: 100%;
        padding: 14px 24px;
        font-size: 1rem;
      }

      /* WhatsApp Button Mobile */
      .floating-whatsapp {
        width: 50px;
        height: 50px;
        font-size: 20px;
        bottom: 20px;
        right: 20px;
      }

      /* Custom Alert Mobile */
      .custom-alert-box {
        margin: 20px;
        padding: 20px;
      }

      .custom-alert-title {
        font-size: 20px;
      }

      .custom-alert-message {
        font-size: 14px;
      }

      .custom-alert-buttons {
        flex-direction: column;
      }

      .custom-alert-btn {
        width: 100%;
      }

      /* Swiper Mobile Optimizations */
      .swiper-button-next,
      .swiper-button-prev {
        width: 35px;
        height: 35px;
      }

      .swiper-button-next:after,
      .swiper-button-prev:after {
        font-size: 16px;
      }

      .swiper-pagination-bullet {
        width: 8px;
        height: 8px;
      }

      /* General Mobile Optimizations */
      .container {
        padding-left: 1rem;
        padding-right: 1rem;
      }

      .px-6 {
        padding-left: 1rem;
        padding-right: 1rem;
      }

      .py-20 {
        padding-top: 3rem;
        padding-bottom: 3rem;
      }

      .py-16 {
        padding-top: 2.5rem;
        padding-bottom: 2.5rem;
      }

      .text-5xl {
        font-size: 2.5rem;
      }

      .text-4xl {
        font-size: 2rem;
      }

      .text-3xl {
        font-size: 1.75rem;
      }

      .text-xl {
        font-size: 1.125rem;
      }

      .text-lg {
        font-size: 1rem;
      }

      /* Preserve animations on mobile */
      .hover-lift:hover {
        transform: translateY(-5px) scale(1.01);
      }

      .glow-effect {
        animation: glow 2s ease-in-out infinite alternate;
      }

      .magnetic:hover {
        transform: scale(1.02);
      }
    }

    /* Extra Small Mobile Devices */
    @media (max-width: 480px) {
      #home h1 {
        font-size: 2rem !important;
        margin-bottom: 0.75rem;
      }

      #home p {
        font-size: 1rem !important;
        margin-bottom: 1.5rem;
      }

      .navbar-glass .flex {
        height: 60px;
      }

      .navbar-glass .text-2xl {
        font-size: 1.25rem;
      }

      #amenities .grid {
        grid-template-columns: 1fr;
      }

      .custom-alert-box {
        margin: 10px;
        padding: 15px;
      }

      .custom-alert-title {
        font-size: 18px;
      }

      .custom-alert-message {
        font-size: 13px;
      }

      .luxury-btn {
        padding: 12px 20px;
        font-size: 0.85rem;
      }

      .floating-whatsapp {
        width: 45px;
        height: 45px;
        font-size: 18px;
        bottom: 15px;
        right: 15px;
      }
    }

    /* Landscape Mobile Optimization */
    @media (max-width: 768px) and (orientation: landscape) {
      #home {
        min-height: 100vh;
        padding-top: 80px;
      }

      #home h1 {
        font-size: 2.25rem !important;
      }

      #home p {
        font-size: 1rem !important;
      }

      .navbar-glass .flex {
        height: 60px;
      }

      /* Logo extra small screens */
      .navbar-logo {
        height: 2rem !important; /* 32px */
      }
    }

    /* Touch-friendly improvements */
    @media (hover: none) and (pointer: coarse) {
      .hover-lift:hover {
        transform: none;
      }

      .hover-lift:active {
        transform: translateY(-5px) scale(1.01);
      }

      .magnetic:hover {
        transform: none;
      }

      .magnetic:active {
        transform: scale(1.02);
      }

      .luxury-btn:hover {
        transform: none;
        box-shadow: 0 10px 30px rgba(212, 175, 55, 0.4);
      }

      .luxury-btn:active {
        transform: translateY(-2px);
      }

      /* Ensure buttons are touch-friendly */
      .luxury-btn,
      .custom-alert-btn,
      .floating-whatsapp {
        min-height: 44px;
        min-width: 44px;
      }
    }
  </style>

  <!-- Favicon and Icons -->
  <link rel="icon" href="/favicon.ico" />
  <link rel="apple-touch-icon" sizes="180x180" href="/apple-touch-icon.png" />
  <link rel="icon" type="image/png" sizes="32x32" href="/favicon-32x32.png" />
  <link rel="icon" type="image/png" sizes="16x16" href="/favicon-16x16.png" />
  <link rel="manifest" href="/site.webmanifest" />

  <!-- Structured Data (JSON-LD) -->
  <script type="application/ld+json">
  {
    "@context": "https://schema.org",
    "@type": "RealEstateAgent",
    "name": "Green Alley",
    "description": "Premium 4 BHK row houses in Pimple Nilakh, Pune with guaranteed army green zone views and individual terrace provisions for swimming pool & gym.",
    "url": "https://greenalley.in",
    "logo": "https://greenalley.in/img/villa-hero.jpeg",
    "image": "https://greenalley.in/img/villa-hero.jpeg",
    "telephone": ["+91-7507007875", "+91-7020637569"],
    "email": "<EMAIL>",
    "address": {
      "@type": "PostalAddress",
      "streetAddress": "Pimple Nilakh",
      "addressLocality": "Pune",
      "addressRegion": "Maharashtra",
      "postalCode": "411027",
      "addressCountry": "IN"
    },
    "geo": {
      "@type": "GeoCoordinates",
      "latitude": "18.5669",
      "longitude": "73.7855"
    },
    "areaServed": {
      "@type": "City",
      "name": "Pune",
      "sameAs": "https://en.wikipedia.org/wiki/Pune"
    },
    "serviceType": "Real Estate Sales",
    "priceRange": "Premium",
    "hasOfferCatalog": {
      "@type": "OfferCatalog",
      "name": "Green Alley Row Houses",
      "itemListElement": [
        {
          "@type": "Offer",
          "itemOffered": {
            "@type": "House",
            "name": "4 BHK Row House",
            "description": "Luxury 4 BHK row house with army green zone views, terrace provisions for swimming pool and gym",
            "numberOfRooms": "4",
            "floorSize": {
              "@type": "QuantitativeValue",
              "value": "Premium",
              "unitText": "BHK"
            }
          }
        }
      ]
    },
    "sameAs": [
      "https://wa.me/917507007875"
    ]
  }
  </script>

  <!-- Local Business Schema -->
  <script type="application/ld+json">
  {
    "@context": "https://schema.org",
    "@type": "LocalBusiness",
    "name": "Green Alley",
    "description": "Luxury 4 BHK row houses in Pimple Nilakh with army green zone views",
    "url": "https://greenalley.in",
    "telephone": ["+91-7507007875", "+91-7020637569"],
    "email": "<EMAIL>",
    "address": {
      "@type": "PostalAddress",
      "streetAddress": "Pimple Nilakh",
      "addressLocality": "Pune",
      "addressRegion": "Maharashtra",
      "postalCode": "411027",
      "addressCountry": "IN"
    },
    "geo": {
      "@type": "GeoCoordinates",
      "latitude": "18.5669",
      "longitude": "73.7855"
    },
    "openingHours": "Mo-Su 09:00-18:00",
    "priceRange": "Premium"
  }
  </script>
</head>
<body class="text-gray-900 font-sans bg-white">

<!-- Navigation -->
<nav class="fixed top-0 w-full z-50 navbar-glass transition-all duration-300" id="navbar">
  <div class="max-w-7xl mx-auto px-6 lg:px-8">
    <div class="flex justify-between items-center h-20">
      <div class="flex items-center">
        <div class="flex items-center">
          <img src="img/logogreen.png"
               alt="Green Alley - Luxury 4 BHK Row Houses in Pimple Nilakh Pune"
               class="navbar-logo h-12 md:h-14 w-auto object-contain"
               style="filter: drop-shadow(0 2px 8px rgba(212, 175, 55, 0.3));">>
        </div>
      </div>

      <div class="hidden md:flex items-center space-x-8">
        <a href="#home" class="text-white hover:text-yellow-400 transition-colors duration-300 font-medium">Home</a>
        <a href="#about" class="text-white hover:text-yellow-400 transition-colors duration-300 font-medium">About</a>
        <a href="#gallery" class="text-white hover:text-yellow-400 transition-colors duration-300 font-medium">Gallery</a>
        <a href="#amenities" class="text-white hover:text-yellow-400 transition-colors duration-300 font-medium">Amenities</a>
        <a href="#location" class="text-white hover:text-yellow-400 transition-colors duration-300 font-medium">Location</a>
        <a href="#faq" class="text-white hover:text-yellow-400 transition-colors duration-300 font-medium">FAQ</a>
        <a href="#contact" class="text-white hover:text-yellow-400 transition-colors duration-300 font-medium">Contact</a>
        <a href="#enquiry" class="luxury-btn text-sm">Book Visit</a>
      </div>

      <div class="md:hidden">
        <button id="mobile-menu-btn" class="text-white focus:outline-none">
          <i class="fas fa-bars text-xl"></i>
        </button>
      </div>
    </div>
  </div>

  <!-- Mobile Menu -->
  <div id="mobile-menu" class="md:hidden hidden bg-black bg-opacity-95 backdrop-blur-lg">
    <div class="px-6 py-4 space-y-4">
      <a href="#home" class="block text-white hover:text-yellow-400 transition-colors duration-300 font-medium">Home</a>
      <a href="#about" class="block text-white hover:text-yellow-400 transition-colors duration-300 font-medium">About</a>
      <a href="#gallery" class="block text-white hover:text-yellow-400 transition-colors duration-300 font-medium">Gallery</a>
      <a href="#amenities" class="block text-white hover:text-yellow-400 transition-colors duration-300 font-medium">Amenities</a>
      <a href="#location" class="block text-white hover:text-yellow-400 transition-colors duration-300 font-medium">Location</a>
      <a href="#faq" class="block text-white hover:text-yellow-400 transition-colors duration-300 font-medium">FAQ</a>
      <a href="#contact" class="block text-white hover:text-yellow-400 transition-colors duration-300 font-medium">Contact</a>
      <a href="#enquiry" class="luxury-btn text-sm inline-block mt-4">Book Visit</a>
    </div>
  </div>
</nav>

<!-- Hero Section -->
<section id="home" class="relative h-screen w-full overflow-hidden">
  <!-- Background Video/Image -->
  <div class="absolute inset-0 z-0">
    <div class="w-full h-full bg-gradient-to-br from-gray-900 via-green-900 to-black"></div>
    <!-- Hero Background Image -->
    <div class="absolute inset-0 bg-cover bg-center bg-no-repeat opacity-50"
         style="background-image: url('img/villa-hero.jpeg');">
    </div>
  </div>

  <!-- Hero Overlay -->
  <div class="absolute inset-0 hero-overlay z-10"></div>

  <!-- Hero Content -->
  <div class="relative z-20 flex flex-col justify-center items-center text-center h-full px-6 text-white">
    <div class="max-w-5xl mx-auto" data-aos="fade-up" data-aos-duration="1000">
      <h1 class="text-5xl md:text-7xl lg:text-8xl font-playfair font-bold mb-6 leading-tight">
        <span style="background: linear-gradient(135deg, #D4AF37 0%, #FFD700 50%, #B8860B 100%); -webkit-background-clip: text; background-clip: text; -webkit-text-fill-color: transparent; color: transparent;">Green Alley</span> <br>
        Luxury 4 BHK Row Houses in Pimple Nilakh
      </h1>
      <p class="text-xl md:text-2xl lg:text-3xl mb-8 max-w-3xl mx-auto font-light leading-relaxed" data-aos="fade-up" data-aos-delay="200">
        Premium Row Houses with Army Green Zone Views | Individual Swimming Pool & Gym Provisions | Pune
      </p>
      <div class="flex flex-col md:flex-row justify-center items-center space-y-4 md:space-y-0 md:space-x-6" data-aos="fade-up" data-aos-delay="400">
        <a href="#enquiry" class="luxury-btn">
          <i class="fas fa-calendar-check mr-2"></i>
          Book Site Visit
        </a>
        <a href="#gallery" class="glass-effect px-8 py-4 rounded-full text-white hover:bg-white hover:text-black transition-all duration-300 font-semibold">
          <i class="fas fa-play mr-2"></i>
          View Gallery
        </a>
      </div>
    </div>

    <!-- Scroll Indicator -->
    <div class="absolute bottom-8 left-1/2 transform -translate-x-1/2 text-white animate-bounce">
      <i class="fas fa-chevron-down text-2xl"></i>
    </div>
  </div>

  <!-- Floating Elements -->
  <div class="absolute top-20 left-10 w-20 h-20 bg-yellow-400 rounded-full opacity-20 animate-pulse"></div>
  <div class="absolute bottom-20 right-10 w-16 h-16 bg-green-400 rounded-full opacity-20 animate-pulse" style="animation-delay: 1s;"></div>
</section>

<!-- Breadcrumb Navigation -->
<nav aria-label="Breadcrumb" class="bg-gray-50 py-4 px-6 md:px-16">
  <div class="max-w-7xl mx-auto">
    <ol class="flex items-center space-x-2 text-sm text-gray-600" itemscope itemtype="https://schema.org/BreadcrumbList">
      <li itemprop="itemListElement" itemscope itemtype="https://schema.org/ListItem">
        <a href="/" itemprop="item" class="hover:text-yellow-600 transition-colors">
          <span itemprop="name">Home</span>
        </a>
        <meta itemprop="position" content="1" />
      </li>
      <li class="text-gray-400">/</li>
      <li itemprop="itemListElement" itemscope itemtype="https://schema.org/ListItem">
        <span itemprop="name" class="text-gray-900 font-medium">4 BHK Row Houses Pimple Nilakh</span>
        <meta itemprop="position" content="2" />
      </li>
    </ol>
  </div>
</nav>

<!-- About Section -->
<section id="about" class="py-20 px-6 md:px-16 bg-white relative overflow-hidden" itemscope itemtype="https://schema.org/RealEstateAgent">
  <div class="max-w-7xl mx-auto">
    <div class="text-center mb-16" data-aos="fade-up">
      <h2 class="text-5xl md:text-6xl font-playfair font-bold mb-6 text-gray-900">
        4 BHK Row Houses in <span style="background: linear-gradient(135deg, #D4AF37 0%, #FFD700 50%, #B8860B 100%); -webkit-background-clip: text; background-clip: text; -webkit-text-fill-color: transparent; color: transparent;">Pimple Nilakh</span> with Army Green Views
      </h2>
      <div class="w-24 h-1 mx-auto mb-8" style="background: linear-gradient(135deg, #D4AF37 0%, #FFD700 50%, #B8860B 100%);"></div>
      <p class="max-w-4xl mx-auto text-xl text-gray-600 leading-relaxed">
        Green Alley offers premium 4 BHK designer row houses in Pimple Nilakh, Pune with guaranteed army green zone views, individual terrace provisions for swimming pool & gym, exceptional connectivity to IT hubs, and luxury living that redefines modern lifestyle in Pune's prime location.
      </p>
    </div>

    <div class="grid lg:grid-cols-2 gap-16 items-center">
      <div data-aos="fade-right">
        <div class="relative">
          <img src="img/villa-main.jpeg"
               alt="Green Alley 4 BHK Row Houses in Pimple Nilakh Pune with Army Green Zone Views"
               class="rounded-2xl luxury-shadow w-full h-96 object-cover">
          <div class="absolute -bottom-6 -right-6 w-32 h-32 luxury-gradient rounded-2xl flex items-center justify-center">
            <div class="text-center text-white">
              <div class="text-2xl font-bold">4</div>
              <div class="text-sm">BHK Row Houses</div>
            </div>
          </div>
        </div>
      </div>

      <div data-aos="fade-left">
        <h3 class="text-3xl font-playfair font-bold mb-6 text-gray-900">Why Choose Green Alley?</h3>
        <div class="space-y-6">
          <div class="flex items-start space-x-4">
            <div class="w-12 h-12 luxury-gradient rounded-full flex items-center justify-center flex-shrink-0">
              <i class="fas fa-home text-white"></i>
            </div>
            <div>
              <h4 class="text-xl font-semibold mb-2 text-gray-900">Designer Row Houses</h4>
              <p class="text-gray-600">Spacious 4 BHK Row Houses with contemporary architecture and premium finishes</p>
            </div>
          </div>

          <div class="flex items-start space-x-4">
            <div class="w-12 h-12 luxury-gradient rounded-full flex items-center justify-center flex-shrink-0">
              <i class="fas fa-tree text-white"></i>
            </div>
            <div>
              <h4 class="text-xl font-semibold mb-2 text-gray-900">Green Views</h4>
              <p class="text-gray-600">Facing army green zone with guaranteed nature views for lifetime</p>
            </div>
          </div>

          <div class="flex items-start space-x-4">
            <div class="w-12 h-12 luxury-gradient rounded-full flex items-center justify-center flex-shrink-0">
              <i class="fas fa-shield-alt text-white"></i>
            </div>
            <div>
              <h4 class="text-xl font-semibold mb-2 text-gray-900">Premium Security</h4>
              <p class="text-gray-600">24x7 security with CCTV surveillance and gated community</p>
            </div>
          </div>

          <div class="flex items-start space-x-4">
            <div class="w-12 h-12 luxury-gradient rounded-full flex items-center justify-center flex-shrink-0">
              <i class="fas fa-wifi text-white"></i>
            </div>
            <div>
              <h4 class="text-xl font-semibold mb-2 text-gray-900">Smart Ready</h4>
              <p class="text-gray-600">Modern automation-ready infrastructure for smart living</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Background Elements -->
  <div class="absolute top-0 right-0 w-64 h-64 bg-gradient-to-br from-yellow-200 to-yellow-400 rounded-full opacity-10 -translate-y-32 translate-x-32"></div>
  <div class="absolute bottom-0 left-0 w-48 h-48 bg-gradient-to-tr from-green-200 to-green-400 rounded-full opacity-10 translate-y-24 -translate-x-24"></div>
</section>

<!-- Location Section -->
<section id="location" class="py-20 px-6 md:px-16 bg-white relative overflow-hidden" itemscope itemtype="https://schema.org/Place">
  <div class="max-w-7xl mx-auto">
    <div class="text-center mb-16" data-aos="fade-up">
      <h2 class="text-5xl md:text-6xl font-playfair font-bold mb-6 text-gray-900">
        <span itemprop="name">Pimple Nilakh</span> - <span style="background: linear-gradient(135deg, #D4AF37 0%, #FFD700 50%, #B8860B 100%); -webkit-background-clip: text; background-clip: text; -webkit-text-fill-color: transparent; color: transparent;">Prime Location</span> in Pune
      </h2>
      <div class="w-24 h-1 mx-auto mb-8" style="background: linear-gradient(135deg, #D4AF37 0%, #FFD700 50%, #B8860B 100%);"></div>
      <p class="max-w-3xl mx-auto text-xl text-gray-600 leading-relaxed" itemprop="description">
        Green Alley row houses strategically located in Pimple Nilakh, Pune with excellent connectivity to IT parks, hospitals, schools, shopping malls, and Mumbai-Pune Expressway. Close to Hinjewadi IT Hub, Balewadi High Street, and upcoming Metro Line 3.
      </p>
      <div itemprop="address" itemscope itemtype="https://schema.org/PostalAddress" class="hidden">
        <span itemprop="streetAddress">Pimple Nilakh</span>
        <span itemprop="addressLocality">Pune</span>
        <span itemprop="addressRegion">Maharashtra</span>
        <span itemprop="postalCode">411027</span>
        <span itemprop="addressCountry">IN</span>
      </div>
      <div itemprop="geo" itemscope itemtype="https://schema.org/GeoCoordinates" class="hidden">
        <meta itemprop="latitude" content="18.5669" />
        <meta itemprop="longitude" content="73.7855" />
      </div>
    </div>

    <div class="grid lg:grid-cols-2 gap-16 items-start mb-16">
      <!-- Location Highlights -->
      <div data-aos="fade-right">
        <div class="grid md:grid-cols-2 gap-8">
          <!-- Hospitals -->
          <div class="bg-gradient-to-br from-red-50 to-red-100 rounded-2xl p-6 hover-lift">
            <div class="flex items-center mb-4">
              <div class="w-12 h-12 bg-red-500 rounded-xl flex items-center justify-center mr-4">
                <i class="fas fa-hospital text-white"></i>
              </div>
              <h3 class="text-xl font-bold text-gray-900">Healthcare</h3>
            </div>
            <ul class="space-y-3 text-gray-700">
              <li class="flex items-center">
                <i class="fas fa-map-marker-alt text-red-500 mr-2"></i>
                Jupiter Hospital – 10 mins
              </li>
              <li class="flex items-center">
                <i class="fas fa-map-marker-alt text-red-500 mr-2"></i>
                Surya Mother & Child – 7 mins
              </li>
              <li class="flex items-center">
                <i class="fas fa-map-marker-alt text-red-500 mr-2"></i>
                Lifepoint Wakad – 10 mins
              </li>
              <li class="flex items-center">
                <i class="fas fa-map-marker-alt text-red-500 mr-2"></i>
                Aditya Birla Hospital – 15 mins
              </li>
            </ul>
          </div>

          <!-- Education -->
          <div class="bg-gradient-to-br from-blue-50 to-blue-100 rounded-2xl p-6 hover-lift">
            <div class="flex items-center mb-4">
              <div class="w-12 h-12 bg-blue-500 rounded-xl flex items-center justify-center mr-4">
                <i class="fas fa-graduation-cap text-white"></i>
              </div>
              <h3 class="text-xl font-bold text-gray-900">Education</h3>
            </div>
            <ul class="space-y-3 text-gray-700">
              <li class="flex items-center">
                <i class="fas fa-map-marker-alt text-blue-500 mr-2"></i>
                Wisdom World Wakad – 10 mins
              </li>
              <li class="flex items-center">
                <i class="fas fa-map-marker-alt text-blue-500 mr-2"></i>
                Indira National – 15 mins
              </li>
              <li class="flex items-center">
                <i class="fas fa-map-marker-alt text-blue-500 mr-2"></i>
                MITCON Balewadi – 7 mins
              </li>
              <li class="flex items-center">
                <i class="fas fa-map-marker-alt text-blue-500 mr-2"></i>
                International Schools Nearby
              </li>
            </ul>
          </div>

          <!-- Shopping -->
          <div class="bg-gradient-to-br from-purple-50 to-purple-100 rounded-2xl p-6 hover-lift">
            <div class="flex items-center mb-4">
              <div class="w-12 h-12 bg-purple-500 rounded-xl flex items-center justify-center mr-4">
                <i class="fas fa-shopping-bag text-white"></i>
              </div>
              <h3 class="text-xl font-bold text-gray-900">Shopping</h3>
            </div>
            <ul class="space-y-3 text-gray-700">
              <li class="flex items-center">
                <i class="fas fa-map-marker-alt text-purple-500 mr-2"></i>
                Westend Mall Aundh – 10 mins
              </li>
              <li class="flex items-center">
                <i class="fas fa-map-marker-alt text-purple-500 mr-2"></i>
                Phoenix Marketcity – 12 mins
              </li>
              <li class="flex items-center">
                <i class="fas fa-map-marker-alt text-purple-500 mr-2"></i>
                Balewadi High Street – 10 mins
              </li>
              <li class="flex items-center">
                <i class="fas fa-map-marker-alt text-purple-500 mr-2"></i>
                Local Markets Nearby
              </li>
            </ul>
          </div>

          <!-- Transport -->
          <div class="bg-gradient-to-br from-green-50 to-green-100 rounded-2xl p-6 hover-lift">
            <div class="flex items-center mb-4">
              <div class="w-12 h-12 bg-green-500 rounded-xl flex items-center justify-center mr-4">
                <i class="fas fa-subway text-white"></i>
              </div>
              <h3 class="text-xl font-bold text-gray-900">Transport</h3>
            </div>
            <ul class="space-y-3 text-gray-700">
              <li class="flex items-center">
                <i class="fas fa-map-marker-alt text-green-500 mr-2"></i>
                Metro Line 3 – 1.5 km
              </li>
              <li class="flex items-center">
                <i class="fas fa-map-marker-alt text-green-500 mr-2"></i>
                Mumbai-Pune Expressway – 10 mins
              </li>
              <li class="flex items-center">
                <i class="fas fa-map-marker-alt text-green-500 mr-2"></i>
                BRT Corridor – Nearby
              </li>
              <li class="flex items-center">
                <i class="fas fa-map-marker-alt text-green-500 mr-2"></i>
                Airport – 45 mins
              </li>
            </ul>
          </div>
        </div>
      </div>

      <!-- Interactive Map -->
      <div data-aos="fade-left">
        <div class="bg-white rounded-2xl luxury-shadow overflow-hidden">
          <div class="p-6 bg-gradient-to-r from-gray-900 to-gray-800">
            <h3 class="text-2xl font-bold text-white mb-2">Interactive Location Map</h3>
            <p class="text-gray-300">Explore the neighborhood and nearby amenities</p>
          </div>
          <div class="relative">
            <iframe class="w-full h-96"
              src="https://www.google.com/maps/embed?pb=!1m18!1m12!1m3!1d3782.0362663249555!2d73.7855!3d18.5669!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0x3bc2b94e3e121f7f%3A0x7ad58f3!2sPimple%20Nilakh%2C%20Pune%2C%20Maharashtra!5e0!3m2!1sen!2sin!4v1719821234567"
              allowfullscreen="" loading="lazy"></iframe>
            <div class="absolute top-4 left-4 bg-white rounded-lg p-3 luxury-shadow">
              <div class="flex items-center">
                <div class="w-3 h-3 bg-red-500 rounded-full mr-2"></div>
                <span class="text-sm font-semibold text-gray-900">Green Alley</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Location Image Showcase -->
    <div class="mt-20" data-aos="fade-up" data-aos-delay="600">
      <div class="text-center mb-12">
        <h3 class="text-3xl font-playfair font-bold mb-4 text-gray-900">Experience the Location</h3>
        <p class="text-gray-600 max-w-2xl mx-auto">Discover the beauty and convenience of our prime location in Pimple Nilakh</p>
      </div>

      <div class="grid md:grid-cols-3 gap-6">
        <div class="group cursor-pointer hover-lift">
          <div class="relative overflow-hidden rounded-2xl luxury-shadow">
            <img src="img/villa-hero.jpeg"
                 alt="Green Alley Location View"
                 class="w-full h-64 object-cover transition-transform duration-500 group-hover:scale-110">
            <div class="absolute inset-0 bg-gradient-to-t from-black/60 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300">
              <div class="absolute bottom-4 left-4 text-white">
                <h4 class="text-lg font-semibold">Scenic Views</h4>
                <p class="text-sm text-gray-200">Nature-facing location</p>
              </div>
            </div>
          </div>
        </div>

        <div class="group cursor-pointer hover-lift">
          <div class="relative overflow-hidden rounded-2xl luxury-shadow">
            <img src="img/villa-main.jpeg"
                 alt="Green Alley Connectivity"
                 class="w-full h-64 object-cover transition-transform duration-500 group-hover:scale-110">
            <div class="absolute inset-0 bg-gradient-to-t from-black/60 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300">
              <div class="absolute bottom-4 left-4 text-white">
                <h4 class="text-lg font-semibold">Prime Connectivity</h4>
                <p class="text-sm text-gray-200">Easy access to all areas</p>
              </div>
            </div>
          </div>
        </div>

        <div class="group cursor-pointer hover-lift">
          <div class="relative overflow-hidden rounded-2xl luxury-shadow">
            <img src="img/villa-exterior-1.jpeg"
                 alt="Green Alley Neighborhood"
                 class="w-full h-64 object-cover transition-transform duration-500 group-hover:scale-110">
            <div class="absolute inset-0 bg-gradient-to-t from-black/60 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300">
              <div class="absolute bottom-4 left-4 text-white">
                <h4 class="text-lg font-semibold">Premium Neighborhood</h4>
                <p class="text-sm text-gray-200">Upscale community living</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Background Elements -->
  <div class="absolute top-0 left-0 w-64 h-64 bg-gradient-to-br from-yellow-200 to-yellow-400 rounded-full opacity-10 -translate-y-32 -translate-x-32"></div>
  <div class="absolute bottom-0 right-0 w-48 h-48 bg-gradient-to-tl from-green-200 to-green-400 rounded-full opacity-10 translate-y-24 translate-x-24"></div>
</section>

<!-- Amenities Section -->
<section id="amenities" class="py-20 px-6 md:px-16 bg-gray-50 relative overflow-hidden">
  <div class="max-w-7xl mx-auto">
    <div class="text-center mb-16" data-aos="fade-up">
      <h2 class="text-5xl md:text-6xl font-playfair font-bold mb-6 text-gray-900">
        Premium <span style="background: linear-gradient(135deg, #D4AF37 0%, #FFD700 50%, #B8860B 100%); -webkit-background-clip: text; background-clip: text; -webkit-text-fill-color: transparent; color: transparent;">Amenities</span> & Features
      </h2>
      <div class="w-24 h-1 mx-auto mb-8" style="background: linear-gradient(135deg, #D4AF37 0%, #FFD700 50%, #B8860B 100%);"></div>
      <p class="max-w-3xl mx-auto text-xl text-gray-600 leading-relaxed">
        4 BHK row houses with individual terrace provisions for swimming pool & gym, army green zone views, smart home ready infrastructure, and premium security in Pimple Nilakh, Pune
      </p>
    </div>

    <div class="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
      <!-- Amenity Card 1 -->
      <div class="group bg-white rounded-2xl p-8 luxury-shadow hover-lift" data-aos="fade-up" data-aos-delay="100">
        <div class="w-16 h-16 luxury-gradient rounded-2xl flex items-center justify-center mb-6 group-hover:scale-110 transition-transform duration-300">
          <i class="fas fa-home text-white text-2xl"></i>
        </div>
        <h3 class="text-2xl font-bold mb-4 text-gray-900 group-hover:text-yellow-600 transition-colors">4 BHK Designer Row Houses</h3>
        <p class="text-gray-600 leading-relaxed">Spacious, airy interiors with luxury finishes and contemporary architecture</p>
      </div>

      <!-- Amenity Card 2 -->
      <div class="group bg-white rounded-2xl p-8 luxury-shadow hover-lift" data-aos="fade-up" data-aos-delay="200">
        <div class="w-16 h-16 luxury-gradient rounded-2xl flex items-center justify-center mb-6 group-hover:scale-110 transition-transform duration-300">
          <i class="fas fa-tree text-white text-2xl"></i>
        </div>
        <h3 class="text-2xl font-bold mb-4 text-gray-900 group-hover:text-yellow-600 transition-colors">Army Green Zone Views</h3>
        <p class="text-gray-600 leading-relaxed">Guaranteed green views for life with unobstructed nature vistas</p>
      </div>

      <!-- Amenity Card 3 -->
      <div class="group bg-white rounded-2xl p-8 luxury-shadow hover-lift" data-aos="fade-up" data-aos-delay="300">
        <div class="w-16 h-16 luxury-gradient rounded-2xl flex items-center justify-center mb-6 group-hover:scale-110 transition-transform duration-300">
          <i class="fas fa-car text-white text-2xl"></i>
        </div>
        <h3 class="text-2xl font-bold mb-4 text-gray-900 group-hover:text-yellow-600 transition-colors">Private Parking</h3>
        <p class="text-gray-600 leading-relaxed">Dedicated covered car park per unit with additional guest parking</p>
      </div>

      <!-- Amenity Card 4 -->
      <div class="group bg-white rounded-2xl p-8 luxury-shadow hover-lift" data-aos="fade-up" data-aos-delay="400">
        <div class="w-16 h-16 luxury-gradient rounded-2xl flex items-center justify-center mb-6 group-hover:scale-110 transition-transform duration-300">
          <i class="fas fa-seedling text-white text-2xl"></i>
        </div>
        <h3 class="text-2xl font-bold mb-4 text-gray-900 group-hover:text-yellow-600 transition-colors">Terrace Gardens</h3>
        <p class="text-gray-600 leading-relaxed">Private terrace gardens with panoramic views of nature</p>
      </div>

      <!-- Amenity Card 5 -->
      <div class="group bg-white rounded-2xl p-8 luxury-shadow hover-lift" data-aos="fade-up" data-aos-delay="500">
        <div class="w-16 h-16 luxury-gradient rounded-2xl flex items-center justify-center mb-6 group-hover:scale-110 transition-transform duration-300">
          <i class="fas fa-shield-alt text-white text-2xl"></i>
        </div>
        <h3 class="text-2xl font-bold mb-4 text-gray-900 group-hover:text-yellow-600 transition-colors">Gated & Secure</h3>
        <p class="text-gray-600 leading-relaxed">24x7 security with CCTV surveillance and controlled access</p>
      </div>

      <!-- Amenity Card 6 -->
      <div class="group bg-white rounded-2xl p-8 luxury-shadow hover-lift" data-aos="fade-up" data-aos-delay="600">
        <div class="w-16 h-16 luxury-gradient rounded-2xl flex items-center justify-center mb-6 group-hover:scale-110 transition-transform duration-300">
          <i class="fas fa-wifi text-white text-2xl"></i>
        </div>
        <h3 class="text-2xl font-bold mb-4 text-gray-900 group-hover:text-yellow-600 transition-colors">Smart Home Ready</h3>
        <p class="text-gray-600 leading-relaxed">Modern automation-ready infrastructure for intelligent living</p>
      </div>

      <!-- Amenity Card 7 -->
      <div class="group bg-white rounded-2xl p-8 luxury-shadow hover-lift" data-aos="fade-up" data-aos-delay="700">
        <div class="w-16 h-16 luxury-gradient rounded-2xl flex items-center justify-center mb-6 group-hover:scale-110 transition-transform duration-300">
          <i class="fas fa-swimming-pool text-white text-2xl"></i>
        </div>
        <h3 class="text-2xl font-bold mb-4 text-gray-900 group-hover:text-yellow-600 transition-colors">Swimming Pool Provision</h3>
        <p class="text-gray-600 leading-relaxed">Individual terrace provision for private swimming pool setup</p>
      </div>

      <!-- Amenity Card 8 -->
      <div class="group bg-white rounded-2xl p-8 luxury-shadow hover-lift" data-aos="fade-up" data-aos-delay="800">
        <div class="w-16 h-16 luxury-gradient rounded-2xl flex items-center justify-center mb-6 group-hover:scale-110 transition-transform duration-300">
          <i class="fas fa-dumbbell text-white text-2xl"></i>
        </div>
        <h3 class="text-2xl font-bold mb-4 text-gray-900 group-hover:text-yellow-600 transition-colors">Gym Provision</h3>
        <p class="text-gray-600 leading-relaxed">Individual terrace provision for private gymnasium setup</p>
      </div>

      <!-- Amenity Card 9 -->
      <div class="group bg-white rounded-2xl p-8 luxury-shadow hover-lift" data-aos="fade-up" data-aos-delay="900">
        <div class="w-16 h-16 luxury-gradient rounded-2xl flex items-center justify-center mb-6 group-hover:scale-110 transition-transform duration-300">
          <i class="fas fa-building text-white text-2xl"></i>
        </div>
        <h3 class="text-2xl font-bold mb-4 text-gray-900 group-hover:text-yellow-600 transition-colors">Private Row House Living</h3>
        <p class="text-gray-600 leading-relaxed">Independent living with individual terrace amenity provisions</p>
      </div>
    </div>

    <!-- Amenities Visual Showcase -->
    <div class="mt-20" data-aos="fade-up" data-aos-delay="1000">
      <div class="text-center mb-12">
        <h3 class="text-3xl font-playfair font-bold mb-4 text-gray-900">Individual Luxury Living</h3>
        <p class="text-gray-600 max-w-2xl mx-auto">Private row houses with individual terrace provisions for personalized amenities</p>
      </div>

      <div class="grid md:grid-cols-2 gap-8">
        <div class="group cursor-pointer hover-lift">
          <div class="relative overflow-hidden rounded-2xl luxury-shadow">
            <img src="img/villa-exterior-2.jpeg"
                 alt="Green Alley Amenities"
                 class="w-full h-80 object-cover transition-transform duration-500 group-hover:scale-105">
            <div class="absolute inset-0 bg-gradient-to-t from-black/70 via-transparent to-transparent">
              <div class="absolute bottom-6 left-6 text-white">
                <h4 class="text-2xl font-bold mb-2">Terrace Provisions</h4>
                <p class="text-gray-200">Individual amenity provisions for each row house</p>
              </div>
            </div>
          </div>
        </div>

        <div class="group cursor-pointer hover-lift">
          <div class="relative overflow-hidden rounded-2xl luxury-shadow">
            <img src="img/villa-exterior-3.jpeg"
                 alt="Green Alley Community"
                 class="w-full h-80 object-cover transition-transform duration-500 group-hover:scale-105">
            <div class="absolute inset-0 bg-gradient-to-t from-black/70 via-transparent to-transparent">
              <div class="absolute bottom-6 left-6 text-white">
                <h4 class="text-2xl font-bold mb-2">Private Living</h4>
                <p class="text-gray-200">Independent row houses with personal amenity spaces</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Background Elements -->
  <div class="absolute top-0 right-0 w-96 h-96 bg-gradient-to-bl from-yellow-100 to-transparent rounded-full opacity-30"></div>
  <div class="absolute bottom-0 left-0 w-64 h-64 bg-gradient-to-tr from-green-100 to-transparent rounded-full opacity-30"></div>
</section>

<!-- Gallery Section -->
<section id="gallery" class="py-20 px-6 md:px-16 dark-gradient relative overflow-hidden">
  <div class="max-w-7xl mx-auto">
    <div class="text-center mb-16" data-aos="fade-up">
      <h2 class="text-5xl md:text-6xl font-playfair font-bold mb-6 text-white">
        Luxury <span style="background: linear-gradient(135deg, #D4AF37 0%, #FFD700 50%, #B8860B 100%); -webkit-background-clip: text; background-clip: text; -webkit-text-fill-color: transparent; color: transparent;">Gallery</span>
      </h2>
      <div class="w-24 h-1 mx-auto mb-8" style="background: linear-gradient(135deg, #D4AF37 0%, #FFD700 50%, #B8860B 100%);"></div>
      <p class="max-w-3xl mx-auto text-xl text-gray-300 leading-relaxed">
        Explore the exquisite design and premium finishes of our luxury Row Houses
      </p>
    </div>

    <!-- Main Gallery Slider -->
    <div class="mb-16" data-aos="fade-up" data-aos-delay="200">
      <div class="swiper gallery-main luxury-shadow rounded-2xl overflow-hidden">
        <div class="swiper-wrapper">

          <div class="swiper-slide">
            <img src="img/villa-slide-2.jpeg"
                 alt="Green Alley Luxury Row Houses Complex in Pimple Nilakh Pune" class="w-full h-96 md:h-[500px] object-cover">
            <div class="absolute bottom-0 left-0 right-0 bg-gradient-to-t from-black to-transparent p-8">
              <h3 class="text-2xl font-bold text-white mb-2">Villa Complex</h3>
              <p class="text-gray-300">Luxury row Row Houses with modern design</p>
            </div>
          </div>
          <div class="swiper-slide">
            <img src="img/villa-interior-1.jpeg"
                 alt="Premium Interior Design 4 BHK Row House Green Alley Pimple Nilakh" class="w-full h-96 md:h-[500px] object-cover">
            <div class="absolute bottom-0 left-0 right-0 bg-gradient-to-t from-black to-transparent p-8">
              <h3 class="text-2xl font-bold text-white mb-2">Premium Interiors</h3>
              <p class="text-gray-300">Elegant interiors with luxury finishes</p>
            </div>
          </div>
          <div class="swiper-slide">
            <img src="img/villa-interior-2.jpeg"
                 alt="Green Alley Living Space" class="w-full h-96 md:h-[500px] object-cover">
            <div class="absolute bottom-0 left-0 right-0 bg-gradient-to-t from-black to-transparent p-8">
              <h3 class="text-2xl font-bold text-white mb-2">Spacious Living</h3>
              <p class="text-gray-300">Open-plan living with premium amenities</p>
            </div>
          </div>
          <div class="swiper-slide">
            <img src="img/villa-garden.jpeg"
                 alt="Green Alley Garden View" class="w-full h-96 md:h-[500px] object-cover">
            <div class="absolute bottom-0 left-0 right-0 bg-gradient-to-t from-black to-transparent p-8">
              <h3 class="text-2xl font-bold text-white mb-2">Garden Views</h3>
              <p class="text-gray-300">Nature-facing Row Houses with green surroundings</p>
            </div>
          </div>
          <div class="swiper-slide">
            <img src="img/villa-architecture.jpeg"
                 alt="Green Alley Architecture" class="w-full h-96 md:h-[500px] object-cover">
            <div class="absolute bottom-0 left-0 right-0 bg-gradient-to-t from-black to-transparent p-8">
              <h3 class="text-2xl font-bold text-white mb-2">Modern Architecture</h3>
              <p class="text-gray-300">Contemporary design with luxury elements</p>
            </div>
          </div>
        </div>
        <div class="swiper-pagination"></div>
        <div class="swiper-button-next"></div>
        <div class="swiper-button-prev"></div>
      </div>
    </div>

    <!-- Thumbnail Gallery -->
    <div class="grid grid-cols-2 md:grid-cols-4 gap-6" data-aos="fade-up" data-aos-delay="400">
      <div class="group cursor-pointer hover-lift gallery-image-hover">
        <img src="img/villa-exterior-1.jpeg"
             alt="Green Alley Villa Details" class="w-full h-48 object-cover rounded-xl luxury-shadow">
        <div class="mt-4 text-center">
          <h4 class="text-lg font-semibold text-white group-hover:text-yellow-400 transition-colors">Villa Details</h4>
        </div>
      </div>
      <div class="group cursor-pointer hover-lift gallery-image-hover">
        <img src="img/villa-exterior-2.jpeg"
             alt="Green Alley Community" class="w-full h-48 object-cover rounded-xl luxury-shadow">
        <div class="mt-4 text-center">
          <h4 class="text-lg font-semibold text-white group-hover:text-yellow-400 transition-colors">Community</h4>
        </div>
      </div>
      <div class="group cursor-pointer hover-lift gallery-image-hover">
        <img src="img/villa-exterior-3.jpeg"
             alt="Green Alley Amenities" class="w-full h-48 object-cover rounded-xl luxury-shadow">
        <div class="mt-4 text-center">
          <h4 class="text-lg font-semibold text-white group-hover:text-yellow-400 transition-colors">Amenities</h4>
        </div>
      </div>
      <div class="group cursor-pointer hover-lift gallery-image-hover">
        <img src="img/villa-layout.jpeg"
             alt="Green Alley Layout" class="w-full h-48 object-cover rounded-xl luxury-shadow">
        <div class="mt-4 text-center">
          <h4 class="text-lg font-semibold text-white group-hover:text-yellow-400 transition-colors">Layout</h4>
        </div>
      </div>
    </div>
  </div>

  <!-- Background Elements -->
  <div class="absolute top-20 left-10 w-32 h-32 bg-yellow-400 rounded-full opacity-10 animate-pulse"></div>
  <div class="absolute bottom-20 right-10 w-24 h-24 bg-green-400 rounded-full opacity-10 animate-pulse" style="animation-delay: 2s;"></div>
</section>

<!-- Contact Section -->
<section id="contact" class="py-20 px-6 md:px-16 dark-gradient relative overflow-hidden">
  <div class="max-w-7xl mx-auto">
    <div class="text-center mb-16" data-aos="fade-up">
      <h2 class="text-5xl md:text-6xl font-playfair font-bold mb-6 text-white">
        Get In <span style="background: linear-gradient(135deg, #D4AF37 0%, #FFD700 50%, #B8860B 100%); -webkit-background-clip: text; background-clip: text; -webkit-text-fill-color: transparent; color: transparent;">Touch</span>
      </h2>
      <div class="w-24 h-1 mx-auto mb-8" style="background: linear-gradient(135deg, #D4AF37 0%, #FFD700 50%, #B8860B 100%);"></div>
      <p class="max-w-3xl mx-auto text-xl text-gray-300 leading-relaxed">
        Ready to experience luxury living? Contact us today to schedule your site visit
      </p>
    </div>

    <div class="grid lg:grid-cols-2 gap-16 items-start">
      <!-- Contact Form -->
      <div data-aos="fade-right">
        <div class="bg-white rounded-2xl p-8 luxury-shadow">
          <h3 class="text-2xl font-bold mb-6 text-gray-900">Send us a Message</h3>
          <form id="contactForm" class="space-y-6">
            <div class="grid md:grid-cols-2 gap-6">
              <div>
                <label for="firstName" class="block text-sm font-semibold text-gray-700 mb-2">First Name *</label>
                <input type="text" id="firstName" name="firstName" required
                       class="w-full px-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-yellow-500 focus:border-transparent transition-all duration-300">
              </div>
              <div>
                <label for="lastName" class="block text-sm font-semibold text-gray-700 mb-2">Last Name *</label>
                <input type="text" id="lastName" name="lastName" required
                       class="w-full px-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-yellow-500 focus:border-transparent transition-all duration-300">
              </div>
            </div>

            <div>
              <label for="email" class="block text-sm font-semibold text-gray-700 mb-2">Email Address *</label>
              <input type="email" id="email" name="email" required
                     class="w-full px-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-yellow-500 focus:border-transparent transition-all duration-300">
            </div>

            <div>
              <label for="phone" class="block text-sm font-semibold text-gray-700 mb-2">Phone Number *</label>
              <input type="tel" id="phone" name="phone" required
                     class="w-full px-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-yellow-500 focus:border-transparent transition-all duration-300">
            </div>

            <div>
              <label for="interest" class="block text-sm font-semibold text-gray-700 mb-2">I'm Interested In</label>
              <select id="interest" name="interest"
                      class="w-full px-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-yellow-500 focus:border-transparent transition-all duration-300">
                <option value="">Select an option</option>
                <option value="site-visit">Site Visit</option>
                <option value="brochure">Download Brochure</option>
                <option value="pricing">Pricing Information</option>
                <option value="floor-plans">Floor Plans</option>
                <option value="investment">Investment Opportunity</option>
              </select>
            </div>

            <div>
              <label for="message" class="block text-sm font-semibold text-gray-700 mb-2">Message</label>
              <textarea id="message" name="message" rows="4"
                        class="w-full px-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-yellow-500 focus:border-transparent transition-all duration-300"
                        placeholder="Tell us about your requirements..."></textarea>
            </div>

            <button type="submit" class="luxury-btn w-full">
              <i class="fas fa-paper-plane mr-2"></i>
              Send Message
            </button>
          </form>
        </div>
      </div>

      <!-- Contact Information -->
      <div data-aos="fade-left">
        <div class="space-y-8">
          <!-- Quick Contact -->
          <div class="bg-white bg-opacity-10 backdrop-blur-lg rounded-2xl p-8 border border-white border-opacity-20">
            <h3 class="text-2xl font-bold mb-6 text-white">Quick Contact</h3>
            <div class="space-y-6">
              <div class="flex items-center">
                <div class="w-12 h-12 luxury-gradient rounded-xl flex items-center justify-center mr-4">
                  <i class="fas fa-phone text-white"></i>
                </div>
                <div>
                  <p class="text-gray-300 text-sm">Call Us</p>
                  <a href="tel:+917507007875" class="text-white text-lg font-semibold hover:text-yellow-400 transition-colors">+91 7507007875</a>
                  <br>
                  <a href="tel:+917020637569" class="text-white text-lg font-semibold hover:text-yellow-400 transition-colors">+91 7020637569</a>
                </div>
              </div>

              <div class="flex items-center">
                <div class="w-12 h-12 luxury-gradient rounded-xl flex items-center justify-center mr-4">
                  <i class="fas fa-envelope text-white"></i>
                </div>
                <div>
                  <p class="text-gray-300 text-sm">Email Us</p>
                  <a href="mailto:<EMAIL>" class="text-white text-lg font-semibold hover:text-yellow-400 transition-colors"><EMAIL></a>
                </div>
              </div>

              <div class="flex items-center">
                <div class="w-12 h-12 luxury-gradient rounded-xl flex items-center justify-center mr-4">
                  <i class="fas fa-map-marker-alt text-white"></i>
                </div>
                <div>
                  <p class="text-gray-300 text-sm">Visit Us</p>
                  <p class="text-white text-lg font-semibold">Pimple Nilakh, Pune</p>
                </div>
              </div>
            </div>
          </div>

          <!-- WhatsApp Contact -->
          <div class="bg-gradient-to-r from-green-500 to-green-600 rounded-2xl p-8 text-white">
            <div class="flex items-center mb-4">
              <i class="fab fa-whatsapp text-3xl mr-4"></i>
              <div>
                <h3 class="text-xl font-bold">WhatsApp Us</h3>
                <p class="text-green-100">Get instant responses</p>
              </div>
            </div>
            <p class="mb-6 text-green-100">Chat with our property experts for immediate assistance and quick answers to all your queries.</p>
            <a href="https://wa.me/917507007875?text=Hi%2C%20I%27m%20interested%20in%20Green%20Alley%20luxury%20Row Houses.%20Please%20share%20more%20details."
               target="_blank"
               class="bg-white text-green-600 px-6 py-3 rounded-xl font-semibold hover:bg-green-50 transition-colors inline-flex items-center">
              <i class="fab fa-whatsapp mr-2"></i>
              Start Chat
            </a>
          </div>

          <!-- Office Hours -->
          <div class="bg-white bg-opacity-10 backdrop-blur-lg rounded-2xl p-8 border border-white border-opacity-20">
            <h3 class="text-2xl font-bold mb-6 text-white">Office Hours</h3>
            <div class="space-y-3 text-gray-300">
              <div class="flex justify-between">
                <span>Monday - Friday</span>
                <span class="text-white font-semibold">9:00 AM - 7:00 PM</span>
              </div>
              <div class="flex justify-between">
                <span>Saturday</span>
                <span class="text-white font-semibold">9:00 AM - 6:00 PM</span>
              </div>
              <div class="flex justify-between">
                <span>Sunday</span>
                <span class="text-white font-semibold">10:00 AM - 5:00 PM</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Background Elements -->
  <div class="absolute top-0 right-0 w-96 h-96 bg-gradient-to-bl from-yellow-400 to-transparent rounded-full opacity-10"></div>
  <div class="absolute bottom-0 left-0 w-64 h-64 bg-gradient-to-tr from-green-400 to-transparent rounded-full opacity-10"></div>
</section>

<!-- CTA Section -->
<section id="enquiry" class="py-20 px-6 md:px-16 bg-white relative overflow-hidden">
  <div class="max-w-4xl mx-auto text-center">
    <div data-aos="fade-up">
      <h2 class="text-4xl md:text-5xl font-playfair font-bold mb-6 text-gray-900">
        Ready to Experience <span style="background: linear-gradient(135deg, #D4AF37 0%, #FFD700 50%, #B8860B 100%); -webkit-background-clip: text; background-clip: text; -webkit-text-fill-color: transparent; color: transparent;">Luxury Living?</span>
      </h2>
      <p class="text-xl text-gray-600 mb-8 leading-relaxed">
        Your dream villa awaits. Book your exclusive site visit today and discover the Green Alley difference.
      </p>

      <div class="flex flex-col md:flex-row justify-center items-center gap-4 mb-12">
        <a href="tel:+917507007875" class="luxury-btn">
          <i class="fas fa-phone mr-2"></i>
          Call: +91 7507007875
        </a>
        <a href="tel:+917020637569" class="luxury-btn">
          <i class="fas fa-phone mr-2"></i>
          Call: +91 7020637569
        </a>
        <a href="https://wa.me/917507007875?text=Hi%2C%20I%27m%20interested%20in%20booking%20a%20site%20visit%20for%20Green%20Alley%20Row Houses"
           target="_blank"
           class="bg-green-500 hover:bg-green-600 text-white px-8 py-4 rounded-full font-semibold transition-all duration-300 hover:scale-105 hover:shadow-lg">
          <i class="fab fa-whatsapp mr-2"></i>
          WhatsApp Now
        </a>
      </div>

      <div class="grid md:grid-cols-3 gap-8 text-center">
        <div class="p-6">
          <div class="w-16 h-16 luxury-gradient rounded-full flex items-center justify-center mx-auto mb-4">
            <i class="fas fa-calendar-check text-white text-xl"></i>
          </div>
          <h3 class="text-lg font-semibold mb-2 text-gray-900">Book Site Visit</h3>
          <p class="text-gray-600">Schedule a personalized tour</p>
        </div>
        <div class="p-6">
          <div class="w-16 h-16 luxury-gradient rounded-full flex items-center justify-center mx-auto mb-4">
            <i class="fas fa-download text-white text-xl"></i>
          </div>
          <h3 class="text-lg font-semibold mb-2 text-gray-900">Download Brochure</h3>
          <p class="text-gray-600">Get detailed project information</p>
        </div>
        <div class="p-6">
          <div class="w-16 h-16 luxury-gradient rounded-full flex items-center justify-center mx-auto mb-4">
            <i class="fas fa-handshake text-white text-xl"></i>
          </div>
          <h3 class="text-lg font-semibold mb-2 text-gray-900">Expert Consultation</h3>
          <p class="text-gray-600">Speak with our property experts</p>
        </div>
      </div>
    </div>
  </div>

  <!-- Background Elements -->
  <div class="absolute top-0 left-0 w-64 h-64 bg-gradient-to-br from-yellow-200 to-yellow-400 rounded-full opacity-10 -translate-y-32 -translate-x-32"></div>
  <div class="absolute bottom-0 right-0 w-48 h-48 bg-gradient-to-tl from-green-200 to-green-400 rounded-full opacity-10 translate-y-24 translate-x-24"></div>
</section>

<!-- FAQ Section -->
<section id="faq" class="py-20 px-6 md:px-16 bg-white relative overflow-hidden" itemscope itemtype="https://schema.org/FAQPage">
  <div class="max-w-4xl mx-auto">
    <div class="text-center mb-16" data-aos="fade-up">
      <h2 class="text-5xl md:text-6xl font-playfair font-bold mb-6 text-gray-900">
        Frequently Asked <span style="background: linear-gradient(135deg, #D4AF37 0%, #FFD700 50%, #B8860B 100%); -webkit-background-clip: text; background-clip: text; -webkit-text-fill-color: transparent; color: transparent;">Questions</span>
      </h2>
      <div class="w-24 h-1 mx-auto mb-8" style="background: linear-gradient(135deg, #D4AF37 0%, #FFD700 50%, #B8860B 100%);"></div>
      <p class="max-w-3xl mx-auto text-xl text-gray-600 leading-relaxed">
        Get answers to common questions about Green Alley row houses in Pimple Nilakh, Pune
      </p>
    </div>

    <div class="space-y-6" data-aos="fade-up" data-aos-delay="200">
      <!-- FAQ Item 1 -->
      <div class="bg-gray-50 rounded-2xl p-6 hover:shadow-lg transition-shadow duration-300" itemscope itemprop="mainEntity" itemtype="https://schema.org/Question">
        <h3 class="text-xl font-semibold text-gray-900 mb-3" itemprop="name">What is the configuration of Green Alley row houses?</h3>
        <div itemscope itemprop="acceptedAnswer" itemtype="https://schema.org/Answer">
          <p class="text-gray-600 leading-relaxed" itemprop="text">Green Alley offers premium 4 BHK designer row houses in Pimple Nilakh, Pune. Each row house features spacious interiors, modern architecture, individual terrace provisions for swimming pool and gym, and guaranteed army green zone views.</p>
        </div>
      </div>

      <!-- FAQ Item 2 -->
      <div class="bg-gray-50 rounded-2xl p-6 hover:shadow-lg transition-shadow duration-300" itemscope itemprop="mainEntity" itemtype="https://schema.org/Question">
        <h3 class="text-xl font-semibold text-gray-900 mb-3" itemprop="name">Where is Green Alley located in Pune?</h3>
        <div itemscope itemprop="acceptedAnswer" itemtype="https://schema.org/Answer">
          <p class="text-gray-600 leading-relaxed" itemprop="text">Green Alley is strategically located in Pimple Nilakh, Pune with excellent connectivity to IT hubs like Hinjewadi, shopping destinations like Balewadi High Street, and major transport links including the upcoming Metro Line 3 and Mumbai-Pune Expressway.</p>
        </div>
      </div>

      <!-- FAQ Item 3 -->
      <div class="bg-gray-50 rounded-2xl p-6 hover:shadow-lg transition-shadow duration-300" itemscope itemprop="mainEntity" itemtype="https://schema.org/Question">
        <h3 class="text-xl font-semibold text-gray-900 mb-3" itemprop="name">What are the unique features of Green Alley row houses?</h3>
        <div itemscope itemprop="acceptedAnswer" itemtype="https://schema.org/Answer">
          <p class="text-gray-600 leading-relaxed" itemprop="text">Green Alley row houses feature army green zone views, individual terrace provisions for swimming pool and gym setup, smart home ready infrastructure, 24x7 security, private parking, and terrace gardens. Each unit offers independent living with premium amenities.</p>
        </div>
      </div>

      <!-- FAQ Item 4 -->
      <div class="bg-gray-50 rounded-2xl p-6 hover:shadow-lg transition-shadow duration-300" itemscope itemprop="mainEntity" itemtype="https://schema.org/Question">
        <h3 class="text-xl font-semibold text-gray-900 mb-3" itemprop="name">Are there common amenities like swimming pool and gym?</h3>
        <div itemscope itemprop="acceptedAnswer" itemtype="https://schema.org/Answer">
          <p class="text-gray-600 leading-relaxed" itemprop="text">Green Alley provides individual terrace provisions for swimming pool and gym setup rather than common amenities. This allows each homeowner to customize their private amenities according to their preferences and lifestyle needs.</p>
        </div>
      </div>

      <!-- FAQ Item 5 -->
      <div class="bg-gray-50 rounded-2xl p-6 hover:shadow-lg transition-shadow duration-300" itemscope itemprop="mainEntity" itemtype="https://schema.org/Question">
        <h3 class="text-xl font-semibold text-gray-900 mb-3" itemprop="name">How can I book a site visit for Green Alley?</h3>
        <div itemscope itemprop="acceptedAnswer" itemtype="https://schema.org/Answer">
          <p class="text-gray-600 leading-relaxed" itemprop="text">You can book a site visit by calling us at +91 7507007875 or +91 7020637569, or by sending a WhatsApp message. Our property experts will schedule a personalized tour to showcase the row houses and amenities.</p>
        </div>
      </div>
    </div>
  </div>
</section>

<!-- Footer -->
<footer class="py-16 bg-gray-900 text-white relative overflow-hidden">
  <div class="max-w-7xl mx-auto px-6 md:px-16">
    <div class="grid md:grid-cols-4 gap-8 mb-12">
      <!-- Company Info -->
      <div class="md:col-span-2">
        <div class="mb-4">
          <img src="img/logogreen.png"
               alt="Green Alley - Luxury 4 BHK Row Houses in Pimple Nilakh Pune"
               class="h-16 w-auto object-contain"
               style="filter: drop-shadow(0 2px 8px rgba(212, 175, 55, 0.3));">
        </div>
        <p class="text-gray-300 mb-6 leading-relaxed">
          Experience luxury living in the heart of Pune with our premium 4 BHK Row Houses featuring contemporary design, green views, and world-class amenities.
        </p>
        <div class="flex space-x-4">
          <a href="#" class="w-10 h-10 bg-white bg-opacity-10 rounded-full flex items-center justify-center hover:bg-opacity-20 transition-all duration-300">
            <i class="fab fa-facebook-f text-white"></i>
          </a>
          <a href="#" class="w-10 h-10 bg-white bg-opacity-10 rounded-full flex items-center justify-center hover:bg-opacity-20 transition-all duration-300">
            <i class="fab fa-instagram text-white"></i>
          </a>
          <a href="#" class="w-10 h-10 bg-white bg-opacity-10 rounded-full flex items-center justify-center hover:bg-opacity-20 transition-all duration-300">
            <i class="fab fa-youtube text-white"></i>
          </a>
          <a href="#" class="w-10 h-10 bg-white bg-opacity-10 rounded-full flex items-center justify-center hover:bg-opacity-20 transition-all duration-300">
            <i class="fab fa-linkedin-in text-white"></i>
          </a>
        </div>
      </div>

      <!-- Quick Links -->
      <div>
        <h3 class="text-lg font-semibold mb-4 text-white">Quick Links</h3>
        <ul class="space-y-2">
          <li><a href="#home" class="text-gray-300 hover:text-yellow-400 transition-colors">Home</a></li>
          <li><a href="#about" class="text-gray-300 hover:text-yellow-400 transition-colors">About</a></li>
          <li><a href="#gallery" class="text-gray-300 hover:text-yellow-400 transition-colors">Gallery</a></li>
          <li><a href="#amenities" class="text-gray-300 hover:text-yellow-400 transition-colors">Amenities</a></li>
          <li><a href="#location" class="text-gray-300 hover:text-yellow-400 transition-colors">Location</a></li>
          <li><a href="#contact" class="text-gray-300 hover:text-yellow-400 transition-colors">Contact</a></li>
        </ul>
      </div>

      <!-- Contact Info -->
      <div>
        <h3 class="text-lg font-semibold mb-4 text-white">Contact Info</h3>
        <div class="space-y-3">
          <div class="flex items-center">
            <i class="fas fa-phone text-yellow-400 mr-3"></i>
            <div class="flex flex-col">
              <a href="tel:+917507007875" class="text-gray-300 hover:text-white transition-colors">+91 7507007875</a>
              <a href="tel:+917020637569" class="text-gray-300 hover:text-white transition-colors">+91 7020637569</a>
            </div>
          </div>
          <div class="flex items-center">
            <i class="fas fa-envelope text-yellow-400 mr-3"></i>
            <a href="mailto:<EMAIL>" class="text-gray-300 hover:text-white transition-colors"><EMAIL></a>
          </div>
          <div class="flex items-start">
            <i class="fas fa-map-marker-alt text-yellow-400 mr-3 mt-1"></i>
            <span class="text-gray-300">Pimple Nilakh, Pune, Maharashtra</span>
          </div>
        </div>
      </div>
    </div>

    <div class="border-t border-gray-700 pt-8 text-center">
      <p class="text-gray-400">© 2025 Green Alley. All rights reserved. | Developed by TechBurst Solutions</p>
    </div>
  </div>

  <!-- Background Elements -->
  <div class="absolute top-0 right-0 w-64 h-64 bg-gradient-to-bl from-yellow-400 to-transparent rounded-full opacity-5"></div>
  <div class="absolute bottom-0 left-0 w-48 h-48 bg-gradient-to-tr from-green-400 to-transparent rounded-full opacity-5"></div>
</footer>

<!-- Floating WhatsApp Button -->
<a href="https://wa.me/917507007875?text=Hi%2C%20I%27m%20interested%20in%20Green%20Alley%20luxury%20Row Houses.%20Please%20share%20more%20details."
   target="_blank"
   class="floating-whatsapp">
  <i class="fab fa-whatsapp"></i>
</a>

<!-- JavaScript Libraries -->
<script src="https://unpkg.com/swiper@8/swiper-bundle.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/aos/2.3.4/aos.js"></script>

<script>
// IMMEDIATE IMAGE FIX - Run as soon as script loads
(function() {
  function fixAllImages() {
    const images = document.querySelectorAll('img');
    images.forEach(img => {
      img.style.opacity = '1';
      img.style.visibility = 'visible';
      img.style.display = 'block';
      img.style.maxWidth = '100%';
      img.style.height = 'auto';
    });

    // Fix Swiper specifically
    const swiperSlides = document.querySelectorAll('.swiper-slide');
    swiperSlides.forEach(slide => {
      slide.style.opacity = '1';
      slide.style.visibility = 'visible';
      const img = slide.querySelector('img');
      if (img) {
        img.style.opacity = '1';
        img.style.visibility = 'visible';
        img.style.display = 'block';
      }
    });
  }

  // Run immediately
  fixAllImages();

  // Run when DOM is ready
  if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', fixAllImages);
  } else {
    fixAllImages();
  }

  // Run when page is fully loaded
  window.addEventListener('load', fixAllImages);

  // Run periodically for the first few seconds to catch any delayed images
  let attempts = 0;
  const interval = setInterval(() => {
    fixAllImages();
    attempts++;
    if (attempts >= 10) {
      clearInterval(interval);
    }
  }, 500);
})();

// Initialize AOS (Animate On Scroll)
AOS.init({
  duration: 800,
  easing: 'ease-in-out',
  once: true,
  offset: 100
});

// Image error handling
function handleImageError(img) {
  console.log('Image failed to load:', img.src);
  img.style.backgroundColor = '#f3f4f6';
  img.style.minHeight = '200px';
  img.style.display = 'flex';
  img.style.alignItems = 'center';
  img.style.justifyContent = 'center';
  img.style.color = '#6b7280';
  img.style.fontSize = '14px';
  img.style.textAlign = 'center';
  img.alt = 'Image loading...';
}

// Add error handlers to all images when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
  const images = document.querySelectorAll('img');
  images.forEach(img => {
    img.addEventListener('error', function() {
      handleImageError(this);
    });

    // Also check if image is already broken
    if (!img.complete || img.naturalHeight === 0) {
      handleImageError(img);
    }
  });
});

// Custom Alert System
class CustomAlert {
  static show(message, title = 'Notification', type = 'info', callback = null) {
    // Remove existing alert if any
    const existingAlert = document.querySelector('.custom-alert-overlay');
    if (existingAlert) {
      existingAlert.remove();
    }

    // Create alert overlay
    const overlay = document.createElement('div');
    overlay.className = `custom-alert-overlay alert-${type}`;

    // Get icon based on type
    const icons = {
      success: '✓',
      error: '✕',
      warning: '⚠',
      info: 'ℹ'
    };

    // Create alert HTML
    overlay.innerHTML = `
      <div class="custom-alert-box">
        <div class="custom-alert-icon">
          ${icons[type] || icons.info}
        </div>
        <div class="custom-alert-title">${title}</div>
        <div class="custom-alert-message">${message}</div>
        <div class="custom-alert-buttons">
          <button class="custom-alert-btn custom-alert-btn-primary" onclick="CustomAlert.close()">
            OK
          </button>
        </div>
      </div>
    `;

    // Add to body
    document.body.appendChild(overlay);

    // Show with animation
    setTimeout(() => {
      overlay.classList.add('show');
      overlay.querySelector('.custom-alert-box').classList.add('shimmer');
    }, 10);

    // Store callback
    if (callback) {
      overlay.dataset.callback = callback.toString();
    }

    // Auto close after 10 seconds for success messages
    if (type === 'success') {
      setTimeout(() => {
        CustomAlert.close();
      }, 10000);
    }
  }

  static close() {
    const overlay = document.querySelector('.custom-alert-overlay');
    if (overlay) {
      // Execute callback if exists
      if (overlay.dataset.callback) {
        try {
          const callback = new Function('return ' + overlay.dataset.callback)();
          if (typeof callback === 'function') {
            callback();
          }
        } catch (e) {
          console.log('Callback execution failed:', e);
        }
      }

      overlay.classList.remove('show');
      setTimeout(() => {
        overlay.remove();
      }, 300);
    }
  }

  static success(message, title = 'Success!', callback = null) {
    this.show(message, title, 'success', callback);
  }

  static error(message, title = 'Error!', callback = null) {
    this.show(message, title, 'error', callback);
  }

  static warning(message, title = 'Warning!', callback = null) {
    this.show(message, title, 'warning', callback);
  }

  static info(message, title = 'Information', callback = null) {
    this.show(message, title, 'info', callback);
  }
}

// Close alert when clicking outside
document.addEventListener('click', (e) => {
  if (e.target.classList.contains('custom-alert-overlay')) {
    CustomAlert.close();
  }
});

// Close alert with Escape key
document.addEventListener('keydown', (e) => {
  if (e.key === 'Escape') {
    CustomAlert.close();
  }
});

// Initialize Swiper for Gallery with enhanced image handling
const gallerySwiper = new Swiper('.gallery-main', {
  loop: true,
  autoplay: {
    delay: 5000,
    disableOnInteraction: false,
  },
  pagination: {
    el: '.swiper-pagination',
    clickable: true,
  },
  navigation: {
    nextEl: '.swiper-button-next',
    prevEl: '.swiper-button-prev',
  },
  effect: 'slide',
  speed: 800,
  slidesPerView: 1,
  spaceBetween: 0,
  centeredSlides: true,
  preloadImages: true,
  updateOnImagesReady: true,
  watchSlidesProgress: true,
  watchSlidesVisibility: true,
  lazy: false, // Disable lazy loading to ensure immediate visibility
  on: {
    init: function() {
      // Force all images to be visible when Swiper initializes
      setTimeout(() => {
        const slides = this.slides;
        slides.forEach(slide => {
          slide.style.opacity = '1';
          slide.style.visibility = 'visible';
          const img = slide.querySelector('img');
          if (img) {
            img.style.opacity = '1';
            img.style.visibility = 'visible';
            img.style.display = 'block';
          }
        });
      }, 100);
    },
    slideChange: function() {
      // Ensure current slide image is visible
      const activeSlide = this.slides[this.activeIndex];
      if (activeSlide) {
        const img = activeSlide.querySelector('img');
        if (img) {
          img.style.opacity = '1';
          img.style.visibility = 'visible';
          img.style.display = 'block';
        }
      }
    }
  }
});

// Mobile Menu Toggle
const mobileMenuBtn = document.getElementById('mobile-menu-btn');
const mobileMenu = document.getElementById('mobile-menu');

mobileMenuBtn.addEventListener('click', () => {
  mobileMenu.classList.toggle('hidden');
});

// Navbar Scroll Effect
window.addEventListener('scroll', () => {
  const navbar = document.getElementById('navbar');
  if (window.scrollY > 100) {
    navbar.classList.add('bg-opacity-95');
  } else {
    navbar.classList.remove('bg-opacity-95');
  }
});

// Smooth Scrolling for Navigation Links
document.querySelectorAll('a[href^="#"]').forEach(anchor => {
  anchor.addEventListener('click', function (e) {
    e.preventDefault();
    const target = document.querySelector(this.getAttribute('href'));
    if (target) {
      target.scrollIntoView({
        behavior: 'smooth',
        block: 'start'
      });
      // Close mobile menu if open
      mobileMenu.classList.add('hidden');
    }
  });
});

// Contact Form Handling
const contactForm = document.getElementById('contactForm');
contactForm.addEventListener('submit', function(e) {
  e.preventDefault();

  // Get form data
  const formData = new FormData(contactForm);
  const firstName = formData.get('firstName');
  const lastName = formData.get('lastName');
  const email = formData.get('email');
  const phone = formData.get('phone');
  const interest = formData.get('interest');
  const message = formData.get('message');

  // Create mailto link
  const subject = `Green Alley Inquiry from ${firstName} ${lastName}`;
  const body = `
Name: ${firstName} ${lastName}
Email: ${email}
Phone: ${phone}
Interest: ${interest}
Message: ${message}

Best regards,
${firstName} ${lastName}
  `;

  const mailtoLink = `mailto:<EMAIL>?subject=${encodeURIComponent(subject)}&body=${encodeURIComponent(body)}`;

  // Open email client
  window.location.href = mailtoLink;

  // Show success message with custom alert
  CustomAlert.success(
    'Your email client will open with a pre-filled message. Please send it to complete your inquiry.',
    'Thank You for Your Inquiry!',
    () => {
      // Reset form after user acknowledges
      contactForm.reset();
    }
  );
});

// Parallax Effect for Hero Section
window.addEventListener('scroll', () => {
  const scrolled = window.pageYOffset;
  const parallaxElements = document.querySelectorAll('.parallax');

  parallaxElements.forEach(element => {
    const speed = element.dataset.speed || 0.5;
    const yPos = -(scrolled * speed);
    element.style.transform = `translateY(${yPos}px)`;
  });
});

// Add loading animation and ensure images are visible
window.addEventListener('load', () => {
  document.body.classList.add('loaded');

  // Force refresh image loading after page load
  setTimeout(() => {
    initImageLoading();

    // Specifically handle Swiper images
    const swiperImages = document.querySelectorAll('.swiper-slide img');
    swiperImages.forEach(img => {
      if (img.style.opacity !== '1') {
        img.style.opacity = '1';
        img.style.transform = 'scale(1)';
      }
    });

    // Update Swiper to ensure proper display
    if (typeof gallerySwiper !== 'undefined') {
      gallerySwiper.update();
    }
  }, 100);
});

// Intersection Observer for animations
const observerOptions = {
  threshold: 0.1,
  rootMargin: '0px 0px -50px 0px'
};

const observer = new IntersectionObserver((entries) => {
  entries.forEach(entry => {
    if (entry.isIntersecting) {
      entry.target.classList.add('fade-in-up');
    }
  });
}, observerOptions);

// Observe elements for animation
document.querySelectorAll('.hover-lift').forEach(el => {
  observer.observe(el);
});

// Mobile Detection and Optimizations
const isMobile = /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);
const isTouch = 'ontouchstart' in window || navigator.maxTouchPoints > 0;

// Mobile-specific optimizations
if (isMobile) {
  // Disable hover effects on mobile
  document.body.classList.add('mobile-device');

  // Optimize animations for mobile
  const style = document.createElement('style');
  style.textContent = `
    .mobile-device .glow-effect {
      animation-duration: 3s;
    }
    .mobile-device .hover-lift {
      transition-duration: 0.2s;
    }
    .mobile-device .luxury-btn {
      transition-duration: 0.2s;
    }
  `;
  document.head.appendChild(style);

  // Prevent zoom on input focus (iOS)
  const inputs = document.querySelectorAll('input, textarea, select');
  inputs.forEach(input => {
    input.addEventListener('focus', () => {
      if (input.style.fontSize !== '16px') {
        input.style.fontSize = '16px';
      }
    });
  });

  // Touch-friendly navigation
  const navLinks = document.querySelectorAll('nav a[href^="#"]');
  navLinks.forEach(link => {
    link.addEventListener('touchend', (e) => {
      e.preventDefault();
      const target = document.querySelector(link.getAttribute('href'));
      if (target) {
        target.scrollIntoView({
          behavior: 'smooth',
          block: 'start'
        });
      }
    });
  });

  // Optimize scroll performance
  let ticking = false;
  function updateScrollPosition() {
    // Throttle scroll events for better performance
    if (!ticking) {
      requestAnimationFrame(() => {
        // Your scroll-based animations here
        ticking = false;
      });
      ticking = true;
    }
  }

  window.addEventListener('scroll', updateScrollPosition, { passive: true });
}

// Touch-specific enhancements
if (isTouch) {
  // Add touch feedback for buttons
  const buttons = document.querySelectorAll('.luxury-btn, .custom-alert-btn, .floating-whatsapp');
  buttons.forEach(button => {
    button.addEventListener('touchstart', () => {
      button.style.transform = 'scale(0.95)';
    }, { passive: true });

    button.addEventListener('touchend', () => {
      setTimeout(() => {
        button.style.transform = '';
      }, 150);
    }, { passive: true });
  });

  // Improve form interaction on touch devices
  const formElements = document.querySelectorAll('input, textarea, select');
  formElements.forEach(element => {
    element.addEventListener('focus', () => {
      element.style.transform = 'scale(1.02)';
      element.style.boxShadow = '0 5px 15px rgba(212, 175, 55, 0.2)';
    });

    element.addEventListener('blur', () => {
      element.style.transform = 'scale(1)';
      element.style.boxShadow = 'none';
    });
  });
}

// Viewport height fix for mobile browsers
function setVH() {
  const vh = window.innerHeight * 0.01;
  document.documentElement.style.setProperty('--vh', `${vh}px`);
}

setVH();
window.addEventListener('resize', setVH);
window.addEventListener('orientationchange', () => {
  setTimeout(setVH, 100);
});

// Prevent horizontal scroll on mobile
function preventHorizontalScroll() {
  const body = document.body;
  const html = document.documentElement;

  if (window.innerWidth <= 768) {
    body.style.overflowX = 'hidden';
    html.style.overflowX = 'hidden';
  } else {
    body.style.overflowX = '';
    html.style.overflowX = '';
  }
}

preventHorizontalScroll();
window.addEventListener('resize', preventHorizontalScroll);

// Enhanced mobile form validation
const mobileContactForm = document.getElementById('contactForm');
if (mobileContactForm && isMobile) {
  // Add mobile-specific form enhancements
  const submitBtn = mobileContactForm.querySelector('button[type="submit"]');

  submitBtn.addEventListener('click', (e) => {
    const requiredFields = mobileContactForm.querySelectorAll('[required]');
    let isValid = true;

    requiredFields.forEach(field => {
      if (!field.value.trim()) {
        field.style.borderColor = '#EF4444';
        field.style.backgroundColor = 'rgba(239, 68, 68, 0.1)';
        isValid = false;

        // Vibrate on error (if supported)
        if (navigator.vibrate) {
          navigator.vibrate(200);
        }

        setTimeout(() => {
          field.style.borderColor = '';
          field.style.backgroundColor = '';
        }, 3000);
      }
    });

    if (!isValid) {
      e.preventDefault();
      CustomAlert.warning('Please fill in all required fields.', 'Missing Information');
    }
  });
}

// Optimize images for mobile
if (isMobile) {
  const images = document.querySelectorAll('img');
  images.forEach(img => {
    img.loading = 'lazy';
    img.style.willChange = 'auto';
  });
}

// Mobile-specific swiper optimizations
if (isMobile && typeof Swiper !== 'undefined') {
  // Add touch-friendly swiper settings
  const swiperElements = document.querySelectorAll('.swiper');
  swiperElements.forEach(element => {
    if (element.swiper) {
      element.swiper.update();
    }
  });
}

// Image Lightbox Functionality
function createLightbox() {
  const lightboxHTML = `
    <div id="lightbox" class="fixed inset-0 bg-black bg-opacity-90 z-50 hidden flex items-center justify-center p-4">
      <div class="relative max-w-4xl max-h-full">
        <img id="lightbox-img" src="" alt="" class="max-w-full max-h-full object-contain rounded-lg">
        <button id="lightbox-close" class="absolute top-4 right-4 text-white text-3xl hover:text-yellow-400 transition-colors">
          <i class="fas fa-times"></i>
        </button>
        <div id="lightbox-caption" class="absolute bottom-4 left-4 text-white">
          <h3 class="text-xl font-bold mb-1"></h3>
          <p class="text-gray-300"></p>
        </div>
      </div>
    </div>
  `;

  document.body.insertAdjacentHTML('beforeend', lightboxHTML);

  const lightbox = document.getElementById('lightbox');
  const lightboxImg = document.getElementById('lightbox-img');
  const lightboxClose = document.getElementById('lightbox-close');
  const lightboxCaption = document.getElementById('lightbox-caption');

  // Close lightbox
  function closeLightbox() {
    lightbox.classList.add('hidden');
    document.body.style.overflow = '';
  }

  lightboxClose.addEventListener('click', closeLightbox);
  lightbox.addEventListener('click', (e) => {
    if (e.target === lightbox) {
      closeLightbox();
    }
  });

  // ESC key to close
  document.addEventListener('keydown', (e) => {
    if (e.key === 'Escape' && !lightbox.classList.contains('hidden')) {
      closeLightbox();
    }
  });

  // Add click handlers to gallery images
  const galleryImages = document.querySelectorAll('.swiper-slide img, .hover-lift img');
  galleryImages.forEach(img => {
    img.style.cursor = 'pointer';
    img.addEventListener('click', () => {
      lightboxImg.src = img.src;
      lightboxImg.alt = img.alt;

      // Set caption from parent elements
      const slideElement = img.closest('.swiper-slide');
      if (slideElement) {
        const title = slideElement.querySelector('h3')?.textContent || img.alt;
        const description = slideElement.querySelector('p')?.textContent || '';
        lightboxCaption.querySelector('h3').textContent = title;
        lightboxCaption.querySelector('p').textContent = description;
      } else {
        const title = img.alt || 'Green Alley Villa';
        lightboxCaption.querySelector('h3').textContent = title;
        lightboxCaption.querySelector('p').textContent = 'Luxury living at its finest';
      }

      lightbox.classList.remove('hidden');
      document.body.style.overflow = 'hidden';
    });
  });
}

// Initialize lightbox when DOM is loaded
document.addEventListener('DOMContentLoaded', createLightbox);

// Enhanced image loading with proper error handling
function initImageLoading() {
  const images = document.querySelectorAll('img[src*="img/"]');

  images.forEach(img => {
    // Set initial styles
    img.style.transition = 'opacity 0.5s ease, transform 0.3s ease';

    // Check if image is already loaded
    if (img.complete && img.naturalHeight !== 0) {
      img.style.opacity = '1';
      img.style.transform = 'scale(1)';
    } else {
      // Set loading state
      img.style.opacity = '0.7';
      img.style.transform = 'scale(0.95)';

      // Handle successful load
      img.onload = () => {
        img.style.opacity = '1';
        img.style.transform = 'scale(1)';
      };

      // Handle load error
      img.onerror = () => {
        console.warn('Failed to load image:', img.src);
        img.style.opacity = '0.5';
        img.style.transform = 'scale(1)';
        // Try to reload the image once
        if (!img.dataset.retried) {
          img.dataset.retried = 'true';
          setTimeout(() => {
            img.src = img.src + '?retry=' + Date.now();
          }, 1000);
        }
      };
    }
  });
}

// Force all images to be visible immediately
function forceImageVisibility() {
  const allImages = document.querySelectorAll('img');
  allImages.forEach(img => {
    img.style.opacity = '1';
    img.style.visibility = 'visible';
    img.style.display = 'block';
  });
}

// Initialize image loading immediately
initImageLoading();
forceImageVisibility();

// Also initialize after DOM content is loaded
document.addEventListener('DOMContentLoaded', () => {
  initImageLoading();
  forceImageVisibility();

  // Ensure Swiper images are visible with multiple attempts
  const ensureSwiperImages = () => {
    const swiperSlides = document.querySelectorAll('.swiper-slide');
    swiperSlides.forEach(slide => {
      slide.style.opacity = '1';
      slide.style.visibility = 'visible';
      const img = slide.querySelector('img');
      if (img) {
        img.style.opacity = '1';
        img.style.visibility = 'visible';
        img.style.display = 'block';
        img.style.maxWidth = '100%';
        img.style.height = 'auto';
      }
    });
  };

  // Run multiple times to ensure images are visible
  setTimeout(ensureSwiperImages, 100);
  setTimeout(ensureSwiperImages, 500);
  setTimeout(ensureSwiperImages, 1000);
  setTimeout(ensureSwiperImages, 2000);
});
</script>

</body>
</html>
