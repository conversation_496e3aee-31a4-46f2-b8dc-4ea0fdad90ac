<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <meta name="description" content="Luxury 4 BHK Row Villas in Pimple Nilakh – Green Alley offers nature-facing villas with premium amenities and smart living." />
  <title>Green Alley | Luxury Villas in Pimple Nilakh</title>

  <!-- Fonts -->
  <link rel="preconnect" href="https://fonts.googleapis.com">
  <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
  <link href="https://fonts.googleapis.com/css2?family=Playfair+Display:wght@400;500;600;700;800;900&family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">

  <!-- CSS Libraries -->
  <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
  <link rel="stylesheet" href="https://unpkg.com/swiper@8/swiper-bundle.min.css" />
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/aos/2.3.4/aos.css" />
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" />

  <!-- Custom Styles -->
  <style>
    :root {
      --primary-gold: #D4AF37;
      --primary-dark: #1a1a1a;
      --primary-green: #2d5016;
      --luxury-gradient: linear-gradient(135deg, #D4AF37 0%, #FFD700 50%, #B8860B 100%);
      --dark-gradient: linear-gradient(135deg, #1a1a1a 0%, #2d2d2d 100%);
    }

    * {
      scroll-behavior: smooth;
    }

    body {
      font-family: 'Inter', sans-serif;
      overflow-x: hidden;
    }

    .font-playfair {
      font-family: 'Playfair Display', serif;
    }

    .luxury-gradient {
      background: var(--luxury-gradient);
    }

    .dark-gradient {
      background: var(--dark-gradient);
    }

    .glass-effect {
      background: rgba(255, 255, 255, 0.1);
      backdrop-filter: blur(10px);
      border: 1px solid rgba(255, 255, 255, 0.2);
    }

    .luxury-shadow {
      box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25), 0 0 0 1px rgba(255, 255, 255, 0.05);
    }

    .hover-lift {
      transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
    }

    .hover-lift:hover {
      transform: translateY(-10px) scale(1.02);
    }

    .parallax {
      transform: translateZ(0);
      will-change: transform;
    }

    .luxury-btn {
      background: var(--luxury-gradient);
      color: white;
      padding: 16px 32px;
      border-radius: 50px;
      font-weight: 600;
      text-transform: uppercase;
      letter-spacing: 1px;
      transition: all 0.3s ease;
      position: relative;
      overflow: hidden;
    }

    .luxury-btn::before {
      content: '';
      position: absolute;
      top: 0;
      left: -100%;
      width: 100%;
      height: 100%;
      background: linear-gradient(90deg, transparent, rgba(255,255,255,0.3), transparent);
      transition: left 0.5s;
    }

    .luxury-btn:hover::before {
      left: 100%;
    }

    .luxury-btn:hover {
      transform: translateY(-2px);
      box-shadow: 0 15px 35px rgba(212, 175, 55, 0.4);
    }

    .floating-whatsapp {
      position: fixed;
      bottom: 30px;
      right: 30px;
      z-index: 1000;
      background: #25D366;
      color: white;
      width: 60px;
      height: 60px;
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 24px;
      box-shadow: 0 8px 25px rgba(37, 211, 102, 0.4);
      animation: pulse 2s infinite;
      transition: all 0.3s ease;
    }

    .floating-whatsapp:hover {
      transform: scale(1.1);
      box-shadow: 0 12px 35px rgba(37, 211, 102, 0.6);
    }

    @keyframes pulse {
      0% { box-shadow: 0 0 0 0 rgba(37, 211, 102, 0.7); }
      70% { box-shadow: 0 0 0 10px rgba(37, 211, 102, 0); }
      100% { box-shadow: 0 0 0 0 rgba(37, 211, 102, 0); }
    }

    @keyframes fadeInUp {
      from {
        opacity: 0;
        transform: translateY(30px);
      }
      to {
        opacity: 1;
        transform: translateY(0);
      }
    }

    .fade-in-up {
      animation: fadeInUp 0.8s ease-out;
    }

    .navbar-glass {
      background: rgba(26, 26, 26, 0.9);
      backdrop-filter: blur(20px);
      border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    }

    .hero-overlay {
      background: linear-gradient(135deg, rgba(26, 26, 26, 0.7) 0%, rgba(45, 80, 22, 0.8) 100%);
    }
  </style>

  <link rel="icon" href="/favicon.ico" />
</head>
<body class="text-gray-900 font-sans bg-white">

<!-- Navigation -->
<nav class="fixed top-0 w-full z-50 navbar-glass transition-all duration-300" id="navbar">
  <div class="max-w-7xl mx-auto px-6 lg:px-8">
    <div class="flex justify-between items-center h-20">
      <div class="flex items-center">
        <div class="text-2xl font-playfair font-bold text-white">
          <span class="luxury-gradient bg-clip-text text-transparent">Green Alley</span>
        </div>
      </div>

      <div class="hidden md:flex items-center space-x-8">
        <a href="#home" class="text-white hover:text-yellow-400 transition-colors duration-300 font-medium">Home</a>
        <a href="#about" class="text-white hover:text-yellow-400 transition-colors duration-300 font-medium">About</a>
        <a href="#gallery" class="text-white hover:text-yellow-400 transition-colors duration-300 font-medium">Gallery</a>
        <a href="#amenities" class="text-white hover:text-yellow-400 transition-colors duration-300 font-medium">Amenities</a>
        <a href="#location" class="text-white hover:text-yellow-400 transition-colors duration-300 font-medium">Location</a>
        <a href="#contact" class="text-white hover:text-yellow-400 transition-colors duration-300 font-medium">Contact</a>
        <a href="#enquiry" class="luxury-btn text-sm">Book Visit</a>
      </div>

      <div class="md:hidden">
        <button id="mobile-menu-btn" class="text-white focus:outline-none">
          <i class="fas fa-bars text-xl"></i>
        </button>
      </div>
    </div>
  </div>

  <!-- Mobile Menu -->
  <div id="mobile-menu" class="md:hidden hidden bg-black bg-opacity-95 backdrop-blur-lg">
    <div class="px-6 py-4 space-y-4">
      <a href="#home" class="block text-white hover:text-yellow-400 transition-colors duration-300 font-medium">Home</a>
      <a href="#about" class="block text-white hover:text-yellow-400 transition-colors duration-300 font-medium">About</a>
      <a href="#gallery" class="block text-white hover:text-yellow-400 transition-colors duration-300 font-medium">Gallery</a>
      <a href="#amenities" class="block text-white hover:text-yellow-400 transition-colors duration-300 font-medium">Amenities</a>
      <a href="#location" class="block text-white hover:text-yellow-400 transition-colors duration-300 font-medium">Location</a>
      <a href="#contact" class="block text-white hover:text-yellow-400 transition-colors duration-300 font-medium">Contact</a>
      <a href="#enquiry" class="luxury-btn text-sm inline-block mt-4">Book Visit</a>
    </div>
  </div>
</nav>

<!-- Hero Section -->
<section id="home" class="relative h-screen w-full overflow-hidden">
  <!-- Background Video/Image -->
  <div class="absolute inset-0 z-0">
    <div class="w-full h-full bg-gradient-to-br from-gray-900 via-green-900 to-black"></div>
    <!-- Placeholder for video - you can add actual video here -->
    <div class="absolute inset-0 bg-cover bg-center bg-no-repeat opacity-40"
         style="background-image: url('https://images.unsplash.com/photo-1600596542815-ffad4c1539a9?ixlib=rb-4.0.3&auto=format&fit=crop&w=2070&q=80');">
    </div>
  </div>

  <!-- Hero Overlay -->
  <div class="absolute inset-0 hero-overlay z-10"></div>

  <!-- Hero Content -->
  <div class="relative z-20 flex flex-col justify-center items-center text-center h-full px-6 text-white">
    <div class="max-w-5xl mx-auto" data-aos="fade-up" data-aos-duration="1000">
      <h1 class="text-5xl md:text-7xl lg:text-8xl font-playfair font-bold mb-6 leading-tight">
        Welcome to <br>
        <span class="luxury-gradient bg-clip-text text-transparent">Green Alley</span>
      </h1>
      <p class="text-xl md:text-2xl lg:text-3xl mb-8 max-w-3xl mx-auto font-light leading-relaxed" data-aos="fade-up" data-aos-delay="200">
        Experience Unparalleled Luxury Living in the Green Heart of Pune
      </p>
      <div class="flex flex-col md:flex-row justify-center items-center space-y-4 md:space-y-0 md:space-x-6" data-aos="fade-up" data-aos-delay="400">
        <a href="#enquiry" class="luxury-btn">
          <i class="fas fa-calendar-check mr-2"></i>
          Book Site Visit
        </a>
        <a href="#gallery" class="glass-effect px-8 py-4 rounded-full text-white hover:bg-white hover:text-black transition-all duration-300 font-semibold">
          <i class="fas fa-play mr-2"></i>
          View Gallery
        </a>
      </div>
    </div>

    <!-- Scroll Indicator -->
    <div class="absolute bottom-8 left-1/2 transform -translate-x-1/2 text-white animate-bounce">
      <i class="fas fa-chevron-down text-2xl"></i>
    </div>
  </div>

  <!-- Floating Elements -->
  <div class="absolute top-20 left-10 w-20 h-20 bg-yellow-400 rounded-full opacity-20 animate-pulse"></div>
  <div class="absolute bottom-20 right-10 w-16 h-16 bg-green-400 rounded-full opacity-20 animate-pulse" style="animation-delay: 1s;"></div>
</section>

<!-- About Section -->
<section id="about" class="py-20 px-6 md:px-16 bg-white relative overflow-hidden">
  <div class="max-w-7xl mx-auto">
    <div class="text-center mb-16" data-aos="fade-up">
      <h2 class="text-5xl md:text-6xl font-playfair font-bold mb-6 text-gray-900">
        Prime Location. <span class="luxury-gradient bg-clip-text text-transparent">Peaceful Living.</span> Premium Lifestyle.
      </h2>
      <div class="w-24 h-1 luxury-gradient mx-auto mb-8"></div>
      <p class="max-w-4xl mx-auto text-xl text-gray-600 leading-relaxed">
        Nestled beside lush army greens in Pimple Nilakh, Green Alley offers 4 BHK designer villas with uninterrupted views, exceptional connectivity, and sophisticated living that redefines luxury.
      </p>
    </div>

    <div class="grid lg:grid-cols-2 gap-16 items-center">
      <div data-aos="fade-right">
        <div class="relative">
          <img src="https://images.unsplash.com/photo-1600607687939-ce8a6c25118c?ixlib=rb-4.0.3&auto=format&fit=crop&w=1000&q=80"
               alt="Luxury Villa"
               class="rounded-2xl luxury-shadow w-full h-96 object-cover">
          <div class="absolute -bottom-6 -right-6 w-32 h-32 luxury-gradient rounded-2xl flex items-center justify-center">
            <div class="text-center text-white">
              <div class="text-2xl font-bold">4</div>
              <div class="text-sm">BHK Villas</div>
            </div>
          </div>
        </div>
      </div>

      <div data-aos="fade-left">
        <h3 class="text-3xl font-playfair font-bold mb-6 text-gray-900">Why Choose Green Alley?</h3>
        <div class="space-y-6">
          <div class="flex items-start space-x-4">
            <div class="w-12 h-12 luxury-gradient rounded-full flex items-center justify-center flex-shrink-0">
              <i class="fas fa-home text-white"></i>
            </div>
            <div>
              <h4 class="text-xl font-semibold mb-2 text-gray-900">Designer Villas</h4>
              <p class="text-gray-600">Spacious 4 BHK villas with contemporary architecture and premium finishes</p>
            </div>
          </div>

          <div class="flex items-start space-x-4">
            <div class="w-12 h-12 luxury-gradient rounded-full flex items-center justify-center flex-shrink-0">
              <i class="fas fa-tree text-white"></i>
            </div>
            <div>
              <h4 class="text-xl font-semibold mb-2 text-gray-900">Green Views</h4>
              <p class="text-gray-600">Facing army green zone with guaranteed nature views for lifetime</p>
            </div>
          </div>

          <div class="flex items-start space-x-4">
            <div class="w-12 h-12 luxury-gradient rounded-full flex items-center justify-center flex-shrink-0">
              <i class="fas fa-shield-alt text-white"></i>
            </div>
            <div>
              <h4 class="text-xl font-semibold mb-2 text-gray-900">Premium Security</h4>
              <p class="text-gray-600">24x7 security with CCTV surveillance and gated community</p>
            </div>
          </div>

          <div class="flex items-start space-x-4">
            <div class="w-12 h-12 luxury-gradient rounded-full flex items-center justify-center flex-shrink-0">
              <i class="fas fa-wifi text-white"></i>
            </div>
            <div>
              <h4 class="text-xl font-semibold mb-2 text-gray-900">Smart Ready</h4>
              <p class="text-gray-600">Modern automation-ready infrastructure for smart living</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Background Elements -->
  <div class="absolute top-0 right-0 w-64 h-64 bg-gradient-to-br from-yellow-200 to-yellow-400 rounded-full opacity-10 -translate-y-32 translate-x-32"></div>
  <div class="absolute bottom-0 left-0 w-48 h-48 bg-gradient-to-tr from-green-200 to-green-400 rounded-full opacity-10 translate-y-24 -translate-x-24"></div>
</section>

<!-- Location Section -->
<section id="location" class="py-20 px-6 md:px-16 bg-white relative overflow-hidden">
  <div class="max-w-7xl mx-auto">
    <div class="text-center mb-16" data-aos="fade-up">
      <h2 class="text-5xl md:text-6xl font-playfair font-bold mb-6 text-gray-900">
        Prime <span class="luxury-gradient bg-clip-text text-transparent">Location</span>
      </h2>
      <div class="w-24 h-1 luxury-gradient mx-auto mb-8"></div>
      <p class="max-w-3xl mx-auto text-xl text-gray-600 leading-relaxed">
        Strategically located in Pimple Nilakh with excellent connectivity to all major landmarks
      </p>
    </div>

    <div class="grid lg:grid-cols-2 gap-16 items-start mb-16">
      <!-- Location Highlights -->
      <div data-aos="fade-right">
        <div class="grid md:grid-cols-2 gap-8">
          <!-- Hospitals -->
          <div class="bg-gradient-to-br from-red-50 to-red-100 rounded-2xl p-6 hover-lift">
            <div class="flex items-center mb-4">
              <div class="w-12 h-12 bg-red-500 rounded-xl flex items-center justify-center mr-4">
                <i class="fas fa-hospital text-white"></i>
              </div>
              <h3 class="text-xl font-bold text-gray-900">Healthcare</h3>
            </div>
            <ul class="space-y-3 text-gray-700">
              <li class="flex items-center">
                <i class="fas fa-map-marker-alt text-red-500 mr-2"></i>
                Jupiter Hospital – 10 mins
              </li>
              <li class="flex items-center">
                <i class="fas fa-map-marker-alt text-red-500 mr-2"></i>
                Surya Mother & Child – 7 mins
              </li>
              <li class="flex items-center">
                <i class="fas fa-map-marker-alt text-red-500 mr-2"></i>
                Lifepoint Wakad – 10 mins
              </li>
              <li class="flex items-center">
                <i class="fas fa-map-marker-alt text-red-500 mr-2"></i>
                Aditya Birla Hospital – 15 mins
              </li>
            </ul>
          </div>

          <!-- Education -->
          <div class="bg-gradient-to-br from-blue-50 to-blue-100 rounded-2xl p-6 hover-lift">
            <div class="flex items-center mb-4">
              <div class="w-12 h-12 bg-blue-500 rounded-xl flex items-center justify-center mr-4">
                <i class="fas fa-graduation-cap text-white"></i>
              </div>
              <h3 class="text-xl font-bold text-gray-900">Education</h3>
            </div>
            <ul class="space-y-3 text-gray-700">
              <li class="flex items-center">
                <i class="fas fa-map-marker-alt text-blue-500 mr-2"></i>
                Wisdom World Wakad – 10 mins
              </li>
              <li class="flex items-center">
                <i class="fas fa-map-marker-alt text-blue-500 mr-2"></i>
                Indira National – 15 mins
              </li>
              <li class="flex items-center">
                <i class="fas fa-map-marker-alt text-blue-500 mr-2"></i>
                MITCON Balewadi – 7 mins
              </li>
              <li class="flex items-center">
                <i class="fas fa-map-marker-alt text-blue-500 mr-2"></i>
                International Schools Nearby
              </li>
            </ul>
          </div>

          <!-- Shopping -->
          <div class="bg-gradient-to-br from-purple-50 to-purple-100 rounded-2xl p-6 hover-lift">
            <div class="flex items-center mb-4">
              <div class="w-12 h-12 bg-purple-500 rounded-xl flex items-center justify-center mr-4">
                <i class="fas fa-shopping-bag text-white"></i>
              </div>
              <h3 class="text-xl font-bold text-gray-900">Shopping</h3>
            </div>
            <ul class="space-y-3 text-gray-700">
              <li class="flex items-center">
                <i class="fas fa-map-marker-alt text-purple-500 mr-2"></i>
                Westend Mall Aundh – 10 mins
              </li>
              <li class="flex items-center">
                <i class="fas fa-map-marker-alt text-purple-500 mr-2"></i>
                Phoenix Marketcity – 12 mins
              </li>
              <li class="flex items-center">
                <i class="fas fa-map-marker-alt text-purple-500 mr-2"></i>
                Balewadi High Street – 10 mins
              </li>
              <li class="flex items-center">
                <i class="fas fa-map-marker-alt text-purple-500 mr-2"></i>
                Local Markets Nearby
              </li>
            </ul>
          </div>

          <!-- Transport -->
          <div class="bg-gradient-to-br from-green-50 to-green-100 rounded-2xl p-6 hover-lift">
            <div class="flex items-center mb-4">
              <div class="w-12 h-12 bg-green-500 rounded-xl flex items-center justify-center mr-4">
                <i class="fas fa-subway text-white"></i>
              </div>
              <h3 class="text-xl font-bold text-gray-900">Transport</h3>
            </div>
            <ul class="space-y-3 text-gray-700">
              <li class="flex items-center">
                <i class="fas fa-map-marker-alt text-green-500 mr-2"></i>
                Metro Line 3 – 1.5 km
              </li>
              <li class="flex items-center">
                <i class="fas fa-map-marker-alt text-green-500 mr-2"></i>
                Mumbai-Pune Expressway – 10 mins
              </li>
              <li class="flex items-center">
                <i class="fas fa-map-marker-alt text-green-500 mr-2"></i>
                BRT Corridor – Nearby
              </li>
              <li class="flex items-center">
                <i class="fas fa-map-marker-alt text-green-500 mr-2"></i>
                Airport – 45 mins
              </li>
            </ul>
          </div>
        </div>
      </div>

      <!-- Interactive Map -->
      <div data-aos="fade-left">
        <div class="bg-white rounded-2xl luxury-shadow overflow-hidden">
          <div class="p-6 bg-gradient-to-r from-gray-900 to-gray-800">
            <h3 class="text-2xl font-bold text-white mb-2">Interactive Location Map</h3>
            <p class="text-gray-300">Explore the neighborhood and nearby amenities</p>
          </div>
          <div class="relative">
            <iframe class="w-full h-96"
              src="https://www.google.com/maps/embed?pb=!1m18!1m12!1m3!1d3782.0362663249555!2d73.7855!3d18.5669!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0x3bc2b94e3e121f7f%3A0x7ad58f3!2sPimple%20Nilakh%2C%20Pune%2C%20Maharashtra!5e0!3m2!1sen!2sin!4v1719821234567"
              allowfullscreen="" loading="lazy"></iframe>
            <div class="absolute top-4 left-4 bg-white rounded-lg p-3 luxury-shadow">
              <div class="flex items-center">
                <div class="w-3 h-3 bg-red-500 rounded-full mr-2"></div>
                <span class="text-sm font-semibold text-gray-900">Green Alley</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Background Elements -->
  <div class="absolute top-0 left-0 w-64 h-64 bg-gradient-to-br from-yellow-200 to-yellow-400 rounded-full opacity-10 -translate-y-32 -translate-x-32"></div>
  <div class="absolute bottom-0 right-0 w-48 h-48 bg-gradient-to-tl from-green-200 to-green-400 rounded-full opacity-10 translate-y-24 translate-x-24"></div>
</section>

<!-- Amenities Section -->
<section id="amenities" class="py-20 px-6 md:px-16 bg-gray-50 relative overflow-hidden">
  <div class="max-w-7xl mx-auto">
    <div class="text-center mb-16" data-aos="fade-up">
      <h2 class="text-5xl md:text-6xl font-playfair font-bold mb-6 text-gray-900">
        Luxury <span class="luxury-gradient bg-clip-text text-transparent">Amenities</span>
      </h2>
      <div class="w-24 h-1 luxury-gradient mx-auto mb-8"></div>
      <p class="max-w-3xl mx-auto text-xl text-gray-600 leading-relaxed">
        Experience world-class amenities designed for the discerning homeowner
      </p>
    </div>

    <div class="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
      <!-- Amenity Card 1 -->
      <div class="group bg-white rounded-2xl p-8 luxury-shadow hover-lift" data-aos="fade-up" data-aos-delay="100">
        <div class="w-16 h-16 luxury-gradient rounded-2xl flex items-center justify-center mb-6 group-hover:scale-110 transition-transform duration-300">
          <i class="fas fa-home text-white text-2xl"></i>
        </div>
        <h3 class="text-2xl font-bold mb-4 text-gray-900 group-hover:text-yellow-600 transition-colors">4 BHK Designer Villas</h3>
        <p class="text-gray-600 leading-relaxed">Spacious, airy interiors with luxury finishes and contemporary architecture</p>
      </div>

      <!-- Amenity Card 2 -->
      <div class="group bg-white rounded-2xl p-8 luxury-shadow hover-lift" data-aos="fade-up" data-aos-delay="200">
        <div class="w-16 h-16 luxury-gradient rounded-2xl flex items-center justify-center mb-6 group-hover:scale-110 transition-transform duration-300">
          <i class="fas fa-tree text-white text-2xl"></i>
        </div>
        <h3 class="text-2xl font-bold mb-4 text-gray-900 group-hover:text-yellow-600 transition-colors">Army Green Zone Views</h3>
        <p class="text-gray-600 leading-relaxed">Guaranteed green views for life with unobstructed nature vistas</p>
      </div>

      <!-- Amenity Card 3 -->
      <div class="group bg-white rounded-2xl p-8 luxury-shadow hover-lift" data-aos="fade-up" data-aos-delay="300">
        <div class="w-16 h-16 luxury-gradient rounded-2xl flex items-center justify-center mb-6 group-hover:scale-110 transition-transform duration-300">
          <i class="fas fa-car text-white text-2xl"></i>
        </div>
        <h3 class="text-2xl font-bold mb-4 text-gray-900 group-hover:text-yellow-600 transition-colors">Private Parking</h3>
        <p class="text-gray-600 leading-relaxed">Dedicated covered car park per unit with additional guest parking</p>
      </div>

      <!-- Amenity Card 4 -->
      <div class="group bg-white rounded-2xl p-8 luxury-shadow hover-lift" data-aos="fade-up" data-aos-delay="400">
        <div class="w-16 h-16 luxury-gradient rounded-2xl flex items-center justify-center mb-6 group-hover:scale-110 transition-transform duration-300">
          <i class="fas fa-seedling text-white text-2xl"></i>
        </div>
        <h3 class="text-2xl font-bold mb-4 text-gray-900 group-hover:text-yellow-600 transition-colors">Terrace Gardens</h3>
        <p class="text-gray-600 leading-relaxed">Private terrace gardens with panoramic views of nature</p>
      </div>

      <!-- Amenity Card 5 -->
      <div class="group bg-white rounded-2xl p-8 luxury-shadow hover-lift" data-aos="fade-up" data-aos-delay="500">
        <div class="w-16 h-16 luxury-gradient rounded-2xl flex items-center justify-center mb-6 group-hover:scale-110 transition-transform duration-300">
          <i class="fas fa-shield-alt text-white text-2xl"></i>
        </div>
        <h3 class="text-2xl font-bold mb-4 text-gray-900 group-hover:text-yellow-600 transition-colors">Gated & Secure</h3>
        <p class="text-gray-600 leading-relaxed">24x7 security with CCTV surveillance and controlled access</p>
      </div>

      <!-- Amenity Card 6 -->
      <div class="group bg-white rounded-2xl p-8 luxury-shadow hover-lift" data-aos="fade-up" data-aos-delay="600">
        <div class="w-16 h-16 luxury-gradient rounded-2xl flex items-center justify-center mb-6 group-hover:scale-110 transition-transform duration-300">
          <i class="fas fa-wifi text-white text-2xl"></i>
        </div>
        <h3 class="text-2xl font-bold mb-4 text-gray-900 group-hover:text-yellow-600 transition-colors">Smart Home Ready</h3>
        <p class="text-gray-600 leading-relaxed">Modern automation-ready infrastructure for intelligent living</p>
      </div>

      <!-- Amenity Card 7 -->
      <div class="group bg-white rounded-2xl p-8 luxury-shadow hover-lift" data-aos="fade-up" data-aos-delay="700">
        <div class="w-16 h-16 luxury-gradient rounded-2xl flex items-center justify-center mb-6 group-hover:scale-110 transition-transform duration-300">
          <i class="fas fa-swimming-pool text-white text-2xl"></i>
        </div>
        <h3 class="text-2xl font-bold mb-4 text-gray-900 group-hover:text-yellow-600 transition-colors">Swimming Pool</h3>
        <p class="text-gray-600 leading-relaxed">Resort-style swimming pool with deck area for relaxation</p>
      </div>

      <!-- Amenity Card 8 -->
      <div class="group bg-white rounded-2xl p-8 luxury-shadow hover-lift" data-aos="fade-up" data-aos-delay="800">
        <div class="w-16 h-16 luxury-gradient rounded-2xl flex items-center justify-center mb-6 group-hover:scale-110 transition-transform duration-300">
          <i class="fas fa-dumbbell text-white text-2xl"></i>
        </div>
        <h3 class="text-2xl font-bold mb-4 text-gray-900 group-hover:text-yellow-600 transition-colors">Fitness Center</h3>
        <p class="text-gray-600 leading-relaxed">Fully equipped gymnasium with modern fitness equipment</p>
      </div>

      <!-- Amenity Card 9 -->
      <div class="group bg-white rounded-2xl p-8 luxury-shadow hover-lift" data-aos="fade-up" data-aos-delay="900">
        <div class="w-16 h-16 luxury-gradient rounded-2xl flex items-center justify-center mb-6 group-hover:scale-110 transition-transform duration-300">
          <i class="fas fa-users text-white text-2xl"></i>
        </div>
        <h3 class="text-2xl font-bold mb-4 text-gray-900 group-hover:text-yellow-600 transition-colors">Community Hall</h3>
        <p class="text-gray-600 leading-relaxed">Elegant community hall for events and social gatherings</p>
      </div>
    </div>
  </div>

  <!-- Background Elements -->
  <div class="absolute top-0 right-0 w-96 h-96 bg-gradient-to-bl from-yellow-100 to-transparent rounded-full opacity-30"></div>
  <div class="absolute bottom-0 left-0 w-64 h-64 bg-gradient-to-tr from-green-100 to-transparent rounded-full opacity-30"></div>
</section>

<!-- Gallery Section -->
<section id="gallery" class="py-20 px-6 md:px-16 dark-gradient relative overflow-hidden">
  <div class="max-w-7xl mx-auto">
    <div class="text-center mb-16" data-aos="fade-up">
      <h2 class="text-5xl md:text-6xl font-playfair font-bold mb-6 text-white">
        Luxury <span class="luxury-gradient bg-clip-text text-transparent">Gallery</span>
      </h2>
      <div class="w-24 h-1 luxury-gradient mx-auto mb-8"></div>
      <p class="max-w-3xl mx-auto text-xl text-gray-300 leading-relaxed">
        Explore the exquisite design and premium finishes of our luxury villas
      </p>
    </div>

    <!-- Main Gallery Slider -->
    <div class="mb-16" data-aos="fade-up" data-aos-delay="200">
      <div class="swiper gallery-main luxury-shadow rounded-2xl overflow-hidden">
        <div class="swiper-wrapper">
          <div class="swiper-slide">
            <img src="https://images.unsplash.com/photo-1600607687939-ce8a6c25118c?ixlib=rb-4.0.3&auto=format&fit=crop&w=2000&q=80"
                 alt="Luxury Villa Exterior" class="w-full h-96 md:h-[500px] object-cover">
            <div class="absolute bottom-0 left-0 right-0 bg-gradient-to-t from-black to-transparent p-8">
              <h3 class="text-2xl font-bold text-white mb-2">Villa Exterior</h3>
              <p class="text-gray-300">Contemporary architecture with premium finishes</p>
            </div>
          </div>
          <div class="swiper-slide">
            <img src="https://images.unsplash.com/photo-1600566753190-17f0baa2a6c3?ixlib=rb-4.0.3&auto=format&fit=crop&w=2000&q=80"
                 alt="Living Room" class="w-full h-96 md:h-[500px] object-cover">
            <div class="absolute bottom-0 left-0 right-0 bg-gradient-to-t from-black to-transparent p-8">
              <h3 class="text-2xl font-bold text-white mb-2">Spacious Living Room</h3>
              <p class="text-gray-300">Open-plan living with luxury interiors</p>
            </div>
          </div>
          <div class="swiper-slide">
            <img src="https://images.unsplash.com/photo-1600210492486-724fe5c67fb0?ixlib=rb-4.0.3&auto=format&fit=crop&w=2000&q=80"
                 alt="Master Bedroom" class="w-full h-96 md:h-[500px] object-cover">
            <div class="absolute bottom-0 left-0 right-0 bg-gradient-to-t from-black to-transparent p-8">
              <h3 class="text-2xl font-bold text-white mb-2">Master Bedroom</h3>
              <p class="text-gray-300">Elegant bedrooms with premium amenities</p>
            </div>
          </div>
          <div class="swiper-slide">
            <img src="https://images.unsplash.com/photo-1600566753086-00f18fb6b3ea?ixlib=rb-4.0.3&auto=format&fit=crop&w=2000&q=80"
                 alt="Modern Kitchen" class="w-full h-96 md:h-[500px] object-cover">
            <div class="absolute bottom-0 left-0 right-0 bg-gradient-to-t from-black to-transparent p-8">
              <h3 class="text-2xl font-bold text-white mb-2">Designer Kitchen</h3>
              <p class="text-gray-300">State-of-the-art kitchen with premium appliances</p>
            </div>
          </div>
          <div class="swiper-slide">
            <img src="https://images.unsplash.com/photo-1600607687644-c7171b42498b?ixlib=rb-4.0.3&auto=format&fit=crop&w=2000&q=80"
                 alt="Terrace Garden" class="w-full h-96 md:h-[500px] object-cover">
            <div class="absolute bottom-0 left-0 right-0 bg-gradient-to-t from-black to-transparent p-8">
              <h3 class="text-2xl font-bold text-white mb-2">Terrace Garden</h3>
              <p class="text-gray-300">Private terrace with panoramic green views</p>
            </div>
          </div>
        </div>
        <div class="swiper-pagination"></div>
        <div class="swiper-button-next"></div>
        <div class="swiper-button-prev"></div>
      </div>
    </div>

    <!-- Thumbnail Gallery -->
    <div class="grid grid-cols-2 md:grid-cols-4 gap-6" data-aos="fade-up" data-aos-delay="400">
      <div class="group cursor-pointer hover-lift">
        <img src="https://images.unsplash.com/photo-1600607687920-4e2a09cf159d?ixlib=rb-4.0.3&auto=format&fit=crop&w=500&q=80"
             alt="Villa Night View" class="w-full h-48 object-cover rounded-xl luxury-shadow">
        <div class="mt-4 text-center">
          <h4 class="text-lg font-semibold text-white group-hover:text-yellow-400 transition-colors">Night View</h4>
        </div>
      </div>
      <div class="group cursor-pointer hover-lift">
        <img src="https://images.unsplash.com/photo-1600607688969-a5bfcd646154?ixlib=rb-4.0.3&auto=format&fit=crop&w=500&q=80"
             alt="Community Area" class="w-full h-48 object-cover rounded-xl luxury-shadow">
        <div class="mt-4 text-center">
          <h4 class="text-lg font-semibold text-white group-hover:text-yellow-400 transition-colors">Community</h4>
        </div>
      </div>
      <div class="group cursor-pointer hover-lift">
        <img src="https://images.unsplash.com/photo-1600607688960-e095ff8d5c6e?ixlib=rb-4.0.3&auto=format&fit=crop&w=500&q=80"
             alt="Swimming Pool" class="w-full h-48 object-cover rounded-xl luxury-shadow">
        <div class="mt-4 text-center">
          <h4 class="text-lg font-semibold text-white group-hover:text-yellow-400 transition-colors">Pool Area</h4>
        </div>
      </div>
      <div class="group cursor-pointer hover-lift">
        <img src="https://images.unsplash.com/photo-1600607688888-1e4e4e8e8e8e?ixlib=rb-4.0.3&auto=format&fit=crop&w=500&q=80"
             alt="Parking Area" class="w-full h-48 object-cover rounded-xl luxury-shadow">
        <div class="mt-4 text-center">
          <h4 class="text-lg font-semibold text-white group-hover:text-yellow-400 transition-colors">Parking</h4>
        </div>
      </div>
    </div>
  </div>

  <!-- Background Elements -->
  <div class="absolute top-20 left-10 w-32 h-32 bg-yellow-400 rounded-full opacity-10 animate-pulse"></div>
  <div class="absolute bottom-20 right-10 w-24 h-24 bg-green-400 rounded-full opacity-10 animate-pulse" style="animation-delay: 2s;"></div>
</section>

<!-- Contact Section -->
<section id="contact" class="py-20 px-6 md:px-16 dark-gradient relative overflow-hidden">
  <div class="max-w-7xl mx-auto">
    <div class="text-center mb-16" data-aos="fade-up">
      <h2 class="text-5xl md:text-6xl font-playfair font-bold mb-6 text-white">
        Get In <span class="luxury-gradient bg-clip-text text-transparent">Touch</span>
      </h2>
      <div class="w-24 h-1 luxury-gradient mx-auto mb-8"></div>
      <p class="max-w-3xl mx-auto text-xl text-gray-300 leading-relaxed">
        Ready to experience luxury living? Contact us today to schedule your site visit
      </p>
    </div>

    <div class="grid lg:grid-cols-2 gap-16 items-start">
      <!-- Contact Form -->
      <div data-aos="fade-right">
        <div class="bg-white rounded-2xl p-8 luxury-shadow">
          <h3 class="text-2xl font-bold mb-6 text-gray-900">Send us a Message</h3>
          <form id="contactForm" class="space-y-6">
            <div class="grid md:grid-cols-2 gap-6">
              <div>
                <label for="firstName" class="block text-sm font-semibold text-gray-700 mb-2">First Name *</label>
                <input type="text" id="firstName" name="firstName" required
                       class="w-full px-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-yellow-500 focus:border-transparent transition-all duration-300">
              </div>
              <div>
                <label for="lastName" class="block text-sm font-semibold text-gray-700 mb-2">Last Name *</label>
                <input type="text" id="lastName" name="lastName" required
                       class="w-full px-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-yellow-500 focus:border-transparent transition-all duration-300">
              </div>
            </div>

            <div>
              <label for="email" class="block text-sm font-semibold text-gray-700 mb-2">Email Address *</label>
              <input type="email" id="email" name="email" required
                     class="w-full px-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-yellow-500 focus:border-transparent transition-all duration-300">
            </div>

            <div>
              <label for="phone" class="block text-sm font-semibold text-gray-700 mb-2">Phone Number *</label>
              <input type="tel" id="phone" name="phone" required
                     class="w-full px-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-yellow-500 focus:border-transparent transition-all duration-300">
            </div>

            <div>
              <label for="interest" class="block text-sm font-semibold text-gray-700 mb-2">I'm Interested In</label>
              <select id="interest" name="interest"
                      class="w-full px-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-yellow-500 focus:border-transparent transition-all duration-300">
                <option value="">Select an option</option>
                <option value="site-visit">Site Visit</option>
                <option value="brochure">Download Brochure</option>
                <option value="pricing">Pricing Information</option>
                <option value="floor-plans">Floor Plans</option>
                <option value="investment">Investment Opportunity</option>
              </select>
            </div>

            <div>
              <label for="message" class="block text-sm font-semibold text-gray-700 mb-2">Message</label>
              <textarea id="message" name="message" rows="4"
                        class="w-full px-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-yellow-500 focus:border-transparent transition-all duration-300"
                        placeholder="Tell us about your requirements..."></textarea>
            </div>

            <button type="submit" class="luxury-btn w-full">
              <i class="fas fa-paper-plane mr-2"></i>
              Send Message
            </button>
          </form>
        </div>
      </div>

      <!-- Contact Information -->
      <div data-aos="fade-left">
        <div class="space-y-8">
          <!-- Quick Contact -->
          <div class="bg-white bg-opacity-10 backdrop-blur-lg rounded-2xl p-8 border border-white border-opacity-20">
            <h3 class="text-2xl font-bold mb-6 text-white">Quick Contact</h3>
            <div class="space-y-6">
              <div class="flex items-center">
                <div class="w-12 h-12 luxury-gradient rounded-xl flex items-center justify-center mr-4">
                  <i class="fas fa-phone text-white"></i>
                </div>
                <div>
                  <p class="text-gray-300 text-sm">Call Us</p>
                  <a href="tel:+917507007875" class="text-white text-lg font-semibold hover:text-yellow-400 transition-colors">+91 7507007875</a>
                </div>
              </div>

              <div class="flex items-center">
                <div class="w-12 h-12 luxury-gradient rounded-xl flex items-center justify-center mr-4">
                  <i class="fas fa-envelope text-white"></i>
                </div>
                <div>
                  <p class="text-gray-300 text-sm">Email Us</p>
                  <a href="mailto:<EMAIL>" class="text-white text-lg font-semibold hover:text-yellow-400 transition-colors"><EMAIL></a>
                </div>
              </div>

              <div class="flex items-center">
                <div class="w-12 h-12 luxury-gradient rounded-xl flex items-center justify-center mr-4">
                  <i class="fas fa-map-marker-alt text-white"></i>
                </div>
                <div>
                  <p class="text-gray-300 text-sm">Visit Us</p>
                  <p class="text-white text-lg font-semibold">Pimple Nilakh, Pune</p>
                </div>
              </div>
            </div>
          </div>

          <!-- WhatsApp Contact -->
          <div class="bg-gradient-to-r from-green-500 to-green-600 rounded-2xl p-8 text-white">
            <div class="flex items-center mb-4">
              <i class="fab fa-whatsapp text-3xl mr-4"></i>
              <div>
                <h3 class="text-xl font-bold">WhatsApp Us</h3>
                <p class="text-green-100">Get instant responses</p>
              </div>
            </div>
            <p class="mb-6 text-green-100">Chat with our property experts for immediate assistance and quick answers to all your queries.</p>
            <a href="https://wa.me/917020637569?text=Hi%2C%20I%27m%20interested%20in%20Green%20Alley%20luxury%20villas.%20Please%20share%20more%20details."
               target="_blank"
               class="bg-white text-green-600 px-6 py-3 rounded-xl font-semibold hover:bg-green-50 transition-colors inline-flex items-center">
              <i class="fab fa-whatsapp mr-2"></i>
              Start Chat
            </a>
          </div>

          <!-- Office Hours -->
          <div class="bg-white bg-opacity-10 backdrop-blur-lg rounded-2xl p-8 border border-white border-opacity-20">
            <h3 class="text-2xl font-bold mb-6 text-white">Office Hours</h3>
            <div class="space-y-3 text-gray-300">
              <div class="flex justify-between">
                <span>Monday - Friday</span>
                <span class="text-white font-semibold">9:00 AM - 7:00 PM</span>
              </div>
              <div class="flex justify-between">
                <span>Saturday</span>
                <span class="text-white font-semibold">9:00 AM - 6:00 PM</span>
              </div>
              <div class="flex justify-between">
                <span>Sunday</span>
                <span class="text-white font-semibold">10:00 AM - 5:00 PM</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Background Elements -->
  <div class="absolute top-0 right-0 w-96 h-96 bg-gradient-to-bl from-yellow-400 to-transparent rounded-full opacity-10"></div>
  <div class="absolute bottom-0 left-0 w-64 h-64 bg-gradient-to-tr from-green-400 to-transparent rounded-full opacity-10"></div>
</section>

<!-- CTA Section -->
<section id="enquiry" class="py-20 px-6 md:px-16 bg-white relative overflow-hidden">
  <div class="max-w-4xl mx-auto text-center">
    <div data-aos="fade-up">
      <h2 class="text-4xl md:text-5xl font-playfair font-bold mb-6 text-gray-900">
        Ready to Experience <span class="luxury-gradient bg-clip-text text-transparent">Luxury Living?</span>
      </h2>
      <p class="text-xl text-gray-600 mb-8 leading-relaxed">
        Your dream villa awaits. Book your exclusive site visit today and discover the Green Alley difference.
      </p>

      <div class="flex flex-col md:flex-row justify-center items-center gap-6 mb-12">
        <a href="tel:+917507007875" class="luxury-btn">
          <i class="fas fa-phone mr-2"></i>
          Call: +91 7507007875
        </a>
        <a href="https://wa.me/917020637569?text=Hi%2C%20I%27m%20interested%20in%20booking%20a%20site%20visit%20for%20Green%20Alley%20villas"
           target="_blank"
           class="bg-green-500 hover:bg-green-600 text-white px-8 py-4 rounded-full font-semibold transition-all duration-300 hover:scale-105 hover:shadow-lg">
          <i class="fab fa-whatsapp mr-2"></i>
          WhatsApp Now
        </a>
      </div>

      <div class="grid md:grid-cols-3 gap-8 text-center">
        <div class="p-6">
          <div class="w-16 h-16 luxury-gradient rounded-full flex items-center justify-center mx-auto mb-4">
            <i class="fas fa-calendar-check text-white text-xl"></i>
          </div>
          <h3 class="text-lg font-semibold mb-2 text-gray-900">Book Site Visit</h3>
          <p class="text-gray-600">Schedule a personalized tour</p>
        </div>
        <div class="p-6">
          <div class="w-16 h-16 luxury-gradient rounded-full flex items-center justify-center mx-auto mb-4">
            <i class="fas fa-download text-white text-xl"></i>
          </div>
          <h3 class="text-lg font-semibold mb-2 text-gray-900">Download Brochure</h3>
          <p class="text-gray-600">Get detailed project information</p>
        </div>
        <div class="p-6">
          <div class="w-16 h-16 luxury-gradient rounded-full flex items-center justify-center mx-auto mb-4">
            <i class="fas fa-handshake text-white text-xl"></i>
          </div>
          <h3 class="text-lg font-semibold mb-2 text-gray-900">Expert Consultation</h3>
          <p class="text-gray-600">Speak with our property experts</p>
        </div>
      </div>
    </div>
  </div>

  <!-- Background Elements -->
  <div class="absolute top-0 left-0 w-64 h-64 bg-gradient-to-br from-yellow-200 to-yellow-400 rounded-full opacity-10 -translate-y-32 -translate-x-32"></div>
  <div class="absolute bottom-0 right-0 w-48 h-48 bg-gradient-to-tl from-green-200 to-green-400 rounded-full opacity-10 translate-y-24 translate-x-24"></div>
</section>

<!-- Footer -->
<footer class="py-16 bg-gray-900 text-white relative overflow-hidden">
  <div class="max-w-7xl mx-auto px-6 md:px-16">
    <div class="grid md:grid-cols-4 gap-8 mb-12">
      <!-- Company Info -->
      <div class="md:col-span-2">
        <div class="text-3xl font-playfair font-bold mb-4">
          <span class="luxury-gradient bg-clip-text text-transparent">Green Alley</span>
        </div>
        <p class="text-gray-300 mb-6 leading-relaxed">
          Experience luxury living in the heart of Pune with our premium 4 BHK villas featuring contemporary design, green views, and world-class amenities.
        </p>
        <div class="flex space-x-4">
          <a href="#" class="w-10 h-10 bg-white bg-opacity-10 rounded-full flex items-center justify-center hover:bg-opacity-20 transition-all duration-300">
            <i class="fab fa-facebook-f text-white"></i>
          </a>
          <a href="#" class="w-10 h-10 bg-white bg-opacity-10 rounded-full flex items-center justify-center hover:bg-opacity-20 transition-all duration-300">
            <i class="fab fa-instagram text-white"></i>
          </a>
          <a href="#" class="w-10 h-10 bg-white bg-opacity-10 rounded-full flex items-center justify-center hover:bg-opacity-20 transition-all duration-300">
            <i class="fab fa-youtube text-white"></i>
          </a>
          <a href="#" class="w-10 h-10 bg-white bg-opacity-10 rounded-full flex items-center justify-center hover:bg-opacity-20 transition-all duration-300">
            <i class="fab fa-linkedin-in text-white"></i>
          </a>
        </div>
      </div>

      <!-- Quick Links -->
      <div>
        <h3 class="text-lg font-semibold mb-4 text-white">Quick Links</h3>
        <ul class="space-y-2">
          <li><a href="#home" class="text-gray-300 hover:text-yellow-400 transition-colors">Home</a></li>
          <li><a href="#about" class="text-gray-300 hover:text-yellow-400 transition-colors">About</a></li>
          <li><a href="#gallery" class="text-gray-300 hover:text-yellow-400 transition-colors">Gallery</a></li>
          <li><a href="#amenities" class="text-gray-300 hover:text-yellow-400 transition-colors">Amenities</a></li>
          <li><a href="#location" class="text-gray-300 hover:text-yellow-400 transition-colors">Location</a></li>
          <li><a href="#contact" class="text-gray-300 hover:text-yellow-400 transition-colors">Contact</a></li>
        </ul>
      </div>

      <!-- Contact Info -->
      <div>
        <h3 class="text-lg font-semibold mb-4 text-white">Contact Info</h3>
        <div class="space-y-3">
          <div class="flex items-center">
            <i class="fas fa-phone text-yellow-400 mr-3"></i>
            <a href="tel:+917507007875" class="text-gray-300 hover:text-white transition-colors">+91 7507007875</a>
          </div>
          <div class="flex items-center">
            <i class="fas fa-envelope text-yellow-400 mr-3"></i>
            <a href="mailto:<EMAIL>" class="text-gray-300 hover:text-white transition-colors"><EMAIL></a>
          </div>
          <div class="flex items-start">
            <i class="fas fa-map-marker-alt text-yellow-400 mr-3 mt-1"></i>
            <span class="text-gray-300">Pimple Nilakh, Pune, Maharashtra</span>
          </div>
        </div>
      </div>
    </div>

    <div class="border-t border-gray-700 pt-8 text-center">
      <p class="text-gray-400">© 2025 Green Alley. All rights reserved. | Developed by TechBurst Solutions</p>
    </div>
  </div>

  <!-- Background Elements -->
  <div class="absolute top-0 right-0 w-64 h-64 bg-gradient-to-bl from-yellow-400 to-transparent rounded-full opacity-5"></div>
  <div class="absolute bottom-0 left-0 w-48 h-48 bg-gradient-to-tr from-green-400 to-transparent rounded-full opacity-5"></div>
</footer>

<!-- Floating WhatsApp Button -->
<a href="https://wa.me/917020637569?text=Hi%2C%20I%27m%20interested%20in%20Green%20Alley%20luxury%20villas.%20Please%20share%20more%20details."
   target="_blank"
   class="floating-whatsapp">
  <i class="fab fa-whatsapp"></i>
</a>

<!-- JavaScript Libraries -->
<script src="https://unpkg.com/swiper@8/swiper-bundle.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/aos/2.3.4/aos.js"></script>

<script>
// Initialize AOS (Animate On Scroll)
AOS.init({
  duration: 800,
  easing: 'ease-in-out',
  once: true,
  offset: 100
});

// Initialize Swiper for Gallery
const gallerySwiper = new Swiper('.gallery-main', {
  loop: true,
  autoplay: {
    delay: 5000,
    disableOnInteraction: false,
  },
  pagination: {
    el: '.swiper-pagination',
    clickable: true,
  },
  navigation: {
    nextEl: '.swiper-button-next',
    prevEl: '.swiper-button-prev',
  },
  effect: 'fade',
  fadeEffect: {
    crossFade: true
  }
});

// Mobile Menu Toggle
const mobileMenuBtn = document.getElementById('mobile-menu-btn');
const mobileMenu = document.getElementById('mobile-menu');

mobileMenuBtn.addEventListener('click', () => {
  mobileMenu.classList.toggle('hidden');
});

// Navbar Scroll Effect
window.addEventListener('scroll', () => {
  const navbar = document.getElementById('navbar');
  if (window.scrollY > 100) {
    navbar.classList.add('bg-opacity-95');
  } else {
    navbar.classList.remove('bg-opacity-95');
  }
});

// Smooth Scrolling for Navigation Links
document.querySelectorAll('a[href^="#"]').forEach(anchor => {
  anchor.addEventListener('click', function (e) {
    e.preventDefault();
    const target = document.querySelector(this.getAttribute('href'));
    if (target) {
      target.scrollIntoView({
        behavior: 'smooth',
        block: 'start'
      });
      // Close mobile menu if open
      mobileMenu.classList.add('hidden');
    }
  });
});

// Contact Form Handling
const contactForm = document.getElementById('contactForm');
contactForm.addEventListener('submit', function(e) {
  e.preventDefault();

  // Get form data
  const formData = new FormData(contactForm);
  const firstName = formData.get('firstName');
  const lastName = formData.get('lastName');
  const email = formData.get('email');
  const phone = formData.get('phone');
  const interest = formData.get('interest');
  const message = formData.get('message');

  // Create mailto link
  const subject = `Green Alley Inquiry from ${firstName} ${lastName}`;
  const body = `
Name: ${firstName} ${lastName}
Email: ${email}
Phone: ${phone}
Interest: ${interest}
Message: ${message}

Best regards,
${firstName} ${lastName}
  `;

  const mailtoLink = `mailto:<EMAIL>?subject=${encodeURIComponent(subject)}&body=${encodeURIComponent(body)}`;

  // Open email client
  window.location.href = mailtoLink;

  // Show success message
  alert('Thank you for your inquiry! Your email client will open with a pre-filled message. Please send it to complete your inquiry.');

  // Reset form
  contactForm.reset();
});

// Parallax Effect for Hero Section
window.addEventListener('scroll', () => {
  const scrolled = window.pageYOffset;
  const parallaxElements = document.querySelectorAll('.parallax');

  parallaxElements.forEach(element => {
    const speed = element.dataset.speed || 0.5;
    const yPos = -(scrolled * speed);
    element.style.transform = `translateY(${yPos}px)`;
  });
});

// Add loading animation
window.addEventListener('load', () => {
  document.body.classList.add('loaded');
});

// Intersection Observer for animations
const observerOptions = {
  threshold: 0.1,
  rootMargin: '0px 0px -50px 0px'
};

const observer = new IntersectionObserver((entries) => {
  entries.forEach(entry => {
    if (entry.isIntersecting) {
      entry.target.classList.add('fade-in-up');
    }
  });
}, observerOptions);

// Observe elements for animation
document.querySelectorAll('.hover-lift').forEach(el => {
  observer.observe(el);
});
</script>

</body>
</html>
