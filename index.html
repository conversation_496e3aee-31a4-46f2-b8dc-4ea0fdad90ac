<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <meta name="description" content="Luxury 4 BHK Row Villas in Pimple Nilakh – Green Alley offers nature-facing villas with premium amenities and smart living." />
  <title>Green Alley | Luxury Villas in Pimple Nilakh</title>

  <!-- Fonts -->
  <link rel="preconnect" href="https://fonts.googleapis.com">
  <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
  <link href="https://fonts.googleapis.com/css2?family=Playfair+Display:wght@400;500;600;700;800;900&family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">

  <!-- CSS Libraries -->
  <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
  <link rel="stylesheet" href="https://unpkg.com/swiper@8/swiper-bundle.min.css" />
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/aos/2.3.4/aos.css" />
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" />

  <!-- Custom Styles -->
  <style>
    :root {
      --primary-gold: #D4AF37;
      --primary-dark: #1a1a1a;
      --primary-green: #2d5016;
      --luxury-gradient: linear-gradient(135deg, #D4AF37 0%, #FFD700 50%, #B8860B 100%);
      --dark-gradient: linear-gradient(135deg, #1a1a1a 0%, #2d2d2d 100%);
    }

    * {
      scroll-behavior: smooth;
    }

    body {
      font-family: 'Inter', sans-serif;
      overflow-x: hidden;
    }

    .font-playfair {
      font-family: 'Playfair Display', serif;
    }

    .luxury-gradient {
      background: linear-gradient(135deg, #D4AF37 0%, #FFD700 50%, #B8860B 100%);
    }

    .dark-gradient {
      background: linear-gradient(135deg, #1a1a1a 0%, #2d2d2d 100%);
    }

    /* Fix for gradient text */
    .luxury-gradient.bg-clip-text {
      background: linear-gradient(135deg, #D4AF37 0%, #FFD700 50%, #B8860B 100%);
      -webkit-background-clip: text;
      background-clip: text;
      -webkit-text-fill-color: transparent;
      color: transparent;
    }

    /* Gradient text class */
    .gradient-text {
      background: linear-gradient(135deg, #D4AF37 0%, #FFD700 50%, #B8860B 100%);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
      background-clip: text;
      color: transparent;
      display: inline-block;
      font-weight: bold;
      background-size: 100%;
      background-repeat: no-repeat;
    }

    /* Enhanced fallback for browsers that don't support background-clip */
    @supports not (-webkit-background-clip: text) {
      .gradient-text {
        color: #D4AF37 !important;
        background: none !important;
        text-shadow: 0 0 10px rgba(212, 175, 55, 0.5);
      }
    }

    /* Firefox specific fix */
    @-moz-document url-prefix() {
      .gradient-text {
        color: #D4AF37;
        background: none;
        text-shadow: 0 0 10px rgba(212, 175, 55, 0.5);
      }
    }

    .glass-effect {
      background: rgba(255, 255, 255, 0.1);
      backdrop-filter: blur(10px);
      border: 1px solid rgba(255, 255, 255, 0.2);
    }

    .luxury-shadow {
      box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25), 0 0 0 1px rgba(255, 255, 255, 0.05);
    }

    .hover-lift {
      transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
      position: relative;
      overflow: hidden;
    }

    .hover-lift:hover {
      transform: translateY(-10px) scale(1.02);
      box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
    }

    .hover-lift::before {
      content: '';
      position: absolute;
      top: 0;
      left: -100%;
      width: 100%;
      height: 100%;
      background: linear-gradient(90deg, transparent, rgba(255,255,255,0.1), transparent);
      transition: left 0.6s ease;
      z-index: 1;
    }

    .hover-lift:hover::before {
      left: 100%;
    }

    .parallax {
      transform: translateZ(0);
      will-change: transform;
    }

    /* Advanced Animations */
    @keyframes float {
      0%, 100% { transform: translateY(0px); }
      50% { transform: translateY(-20px); }
    }

    @keyframes glow {
      0%, 100% { box-shadow: 0 0 20px rgba(212, 175, 55, 0.3); }
      50% { box-shadow: 0 0 40px rgba(212, 175, 55, 0.6); }
    }

    @keyframes slideInLeft {
      0% { transform: translateX(-100px); opacity: 0; }
      100% { transform: translateX(0); opacity: 1; }
    }

    @keyframes slideInRight {
      0% { transform: translateX(100px); opacity: 0; }
      100% { transform: translateX(0); opacity: 1; }
    }

    @keyframes scaleIn {
      0% { transform: scale(0.8); opacity: 0; }
      100% { transform: scale(1); opacity: 1; }
    }

    @keyframes rotateIn {
      0% { transform: rotate(-180deg) scale(0.5); opacity: 0; }
      100% { transform: rotate(0deg) scale(1); opacity: 1; }
    }

    @keyframes shimmer {
      0% { background-position: -200% 0; }
      100% { background-position: 200% 0; }
    }

    .floating {
      animation: float 6s ease-in-out infinite;
    }

    .glow-effect {
      animation: glow 3s ease-in-out infinite;
    }

    .slide-in-left {
      animation: slideInLeft 0.8s ease-out;
    }

    .slide-in-right {
      animation: slideInRight 0.8s ease-out;
    }

    .scale-in {
      animation: scaleIn 0.6s ease-out;
    }

    .rotate-in {
      animation: rotateIn 0.8s ease-out;
    }

    .shimmer-effect {
      background: linear-gradient(90deg, transparent, rgba(255,255,255,0.3), transparent);
      background-size: 200% 100%;
      animation: shimmer 2s infinite;
    }

    /* Magnetic Effect */
    .magnetic {
      transition: transform 0.3s ease;
    }

    .magnetic:hover {
      transform: scale(1.05);
    }

    /* Tilt Effect */
    .tilt-effect {
      transition: transform 0.3s ease;
      transform-style: preserve-3d;
    }

    .tilt-effect:hover {
      transform: perspective(1000px) rotateX(5deg) rotateY(5deg);
    }

    /* Morphing Button */
    .morph-btn {
      position: relative;
      overflow: hidden;
      border-radius: 50px;
      transition: all 0.4s ease;
    }

    .morph-btn::before {
      content: '';
      position: absolute;
      top: 50%;
      left: 50%;
      width: 0;
      height: 0;
      background: rgba(255, 255, 255, 0.2);
      border-radius: 50%;
      transform: translate(-50%, -50%);
      transition: width 0.4s ease, height 0.4s ease;
    }

    .morph-btn:hover::before {
      width: 300px;
      height: 300px;
    }

    /* Liquid Animation */
    .liquid-bg {
      position: relative;
      overflow: hidden;
    }

    .liquid-bg::before {
      content: '';
      position: absolute;
      top: -50%;
      left: -50%;
      width: 200%;
      height: 200%;
      background: radial-gradient(circle, rgba(212, 175, 55, 0.1) 0%, transparent 70%);
      animation: float 8s ease-in-out infinite;
      z-index: -1;
    }

    /* Text Reveal Animation */
    .text-reveal {
      overflow: hidden;
      position: relative;
    }

    .text-reveal::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background: linear-gradient(135deg, #D4AF37 0%, #FFD700 50%, #B8860B 100%);
      transform: translateX(-100%);
      animation: reveal 1.5s ease-out forwards;
      animation-delay: 0.5s;
    }

    @keyframes reveal {
      0% { transform: translateX(-100%); }
      50% { transform: translateX(0%); }
      100% { transform: translateX(100%); }
    }

    /* Particle Effect */
    .particle-bg {
      position: relative;
      overflow: hidden;
    }

    .particle {
      position: absolute;
      width: 4px;
      height: 4px;
      background: rgba(212, 175, 55, 0.6);
      border-radius: 50%;
      animation: particle-float 15s infinite linear;
    }

    @keyframes particle-float {
      0% { transform: translateY(100vh) rotate(0deg); opacity: 0; }
      10% { opacity: 1; }
      90% { opacity: 1; }
      100% { transform: translateY(-100vh) rotate(360deg); opacity: 0; }
    }

    .luxury-btn {
      background: linear-gradient(135deg, #D4AF37 0%, #FFD700 50%, #B8860B 100%);
      color: white;
      padding: 16px 32px;
      border-radius: 50px;
      font-weight: 600;
      text-transform: uppercase;
      letter-spacing: 1px;
      transition: all 0.3s ease;
      position: relative;
      overflow: hidden;
      border: none;
      cursor: pointer;
      display: inline-flex;
      align-items: center;
      justify-content: center;
      text-decoration: none;
    }

    .luxury-btn::before {
      content: '';
      position: absolute;
      top: 0;
      left: -100%;
      width: 100%;
      height: 100%;
      background: linear-gradient(90deg, transparent, rgba(255,255,255,0.3), transparent);
      transition: left 0.5s;
    }

    .luxury-btn:hover::before {
      left: 100%;
    }

    .luxury-btn:hover {
      transform: translateY(-2px);
      box-shadow: 0 15px 35px rgba(212, 175, 55, 0.4);
      background: linear-gradient(135deg, #FFD700 0%, #D4AF37 50%, #B8860B 100%);
    }

    .floating-whatsapp {
      position: fixed;
      bottom: 30px;
      right: 30px;
      z-index: 1000;
      background: linear-gradient(135deg, #25D366 0%, #128C7E 100%);
      color: white;
      width: 60px;
      height: 60px;
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 24px;
      box-shadow: 0 8px 25px rgba(37, 211, 102, 0.4);
      animation: pulse 2s infinite;
      transition: all 0.3s ease;
      cursor: pointer;
      overflow: hidden;
    }

    .floating-whatsapp::before {
      content: '';
      position: absolute;
      top: 0;
      left: -100%;
      width: 100%;
      height: 100%;
      background: linear-gradient(90deg, transparent, rgba(255,255,255,0.3), transparent);
      transition: left 0.5s;
    }

    .floating-whatsapp:hover::before {
      left: 100%;
    }

    .floating-whatsapp:hover {
      transform: scale(1.15) rotate(5deg);
      box-shadow: 0 15px 40px rgba(37, 211, 102, 0.6);
    }

    .floating-whatsapp:active {
      transform: scale(1.05);
    }

    @keyframes pulse {
      0% { box-shadow: 0 0 0 0 rgba(37, 211, 102, 0.7); }
      70% { box-shadow: 0 0 0 10px rgba(37, 211, 102, 0); }
      100% { box-shadow: 0 0 0 0 rgba(37, 211, 102, 0); }
    }

    @keyframes fadeInUp {
      from {
        opacity: 0;
        transform: translateY(30px);
      }
      to {
        opacity: 1;
        transform: translateY(0);
      }
    }

    .fade-in-up {
      animation: fadeInUp 0.8s ease-out;
    }

    .navbar-glass {
      background: rgba(26, 26, 26, 0.9);
      backdrop-filter: blur(20px);
      border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    }

    .hero-overlay {
      background: linear-gradient(135deg, rgba(26, 26, 26, 0.7) 0%, rgba(45, 80, 22, 0.8) 100%);
    }

    /* Ensure gradient text works in navigation */
    .navbar-glass .gradient-text {
      background: linear-gradient(135deg, #D4AF37 0%, #FFD700 50%, #B8860B 100%) !important;
      -webkit-background-clip: text !important;
      -webkit-text-fill-color: transparent !important;
      background-clip: text !important;
      color: transparent !important;
    }

    /* Force gradient text rendering */
    .gradient-text {
      -webkit-font-smoothing: antialiased;
      -moz-osx-font-smoothing: grayscale;
      text-rendering: optimizeLegibility;
    }

    /* Webkit specific gradient text fix */
    @media screen and (-webkit-min-device-pixel-ratio:0) {
      .gradient-text {
        background: linear-gradient(135deg, #D4AF37 0%, #FFD700 50%, #B8860B 100%);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
        color: transparent;
      }
    }

    /* Custom Scrollbar */
    ::-webkit-scrollbar {
      width: 14px;
    }

    ::-webkit-scrollbar-track {
      background: linear-gradient(180deg, #1a1a1a 0%, #2d2d2d 50%, #1a1a1a 100%);
      border-radius: 12px;
      box-shadow: inset 0 0 8px rgba(0, 0, 0, 0.4);
      border: 1px solid rgba(255, 255, 255, 0.05);
    }

    ::-webkit-scrollbar-thumb {
      background: linear-gradient(180deg, #D4AF37 0%, #FFD700 30%, #D4AF37 70%, #B8860B 100%);
      border-radius: 12px;
      box-shadow:
        0 2px 10px rgba(212, 175, 55, 0.4),
        inset 0 1px 0 rgba(255, 255, 255, 0.2),
        inset 0 -1px 0 rgba(0, 0, 0, 0.2);
      border: 2px solid #1a1a1a;
      position: relative;
    }

    ::-webkit-scrollbar-thumb:hover {
      background: linear-gradient(180deg, #FFD700 0%, #D4AF37 30%, #FFD700 70%, #B8860B 100%);
      box-shadow:
        0 4px 20px rgba(212, 175, 55, 0.6),
        inset 0 1px 0 rgba(255, 255, 255, 0.3),
        inset 0 -1px 0 rgba(0, 0, 0, 0.3);
      transform: scaleX(1.1);
    }

    ::-webkit-scrollbar-thumb:active {
      background: linear-gradient(180deg, #B8860B 0%, #D4AF37 30%, #FFD700 70%, #D4AF37 100%);
      box-shadow:
        0 2px 15px rgba(212, 175, 55, 0.8),
        inset 0 2px 4px rgba(0, 0, 0, 0.3);
    }

    ::-webkit-scrollbar-corner {
      background: #1a1a1a;
      border-radius: 12px;
    }

    /* Scrollbar animation */
    ::-webkit-scrollbar-thumb {
      transition: all 0.3s ease;
    }

    /* Custom scrollbar for specific elements */
    .luxury-shadow::-webkit-scrollbar {
      width: 8px;
    }

    .luxury-shadow::-webkit-scrollbar-thumb {
      background: linear-gradient(180deg, rgba(212, 175, 55, 0.8) 0%, rgba(255, 215, 0, 0.6) 100%);
      border-radius: 8px;
    }

    /* Firefox Scrollbar */
    html {
      scrollbar-width: thin;
      scrollbar-color: #D4AF37 #1a1a1a;
    }

    /* Custom Cursor */
    * {
      cursor: none;
    }

    body {
      cursor: none;
    }

    /* Custom cursor elements */
    .custom-cursor {
      position: fixed;
      top: 0;
      left: 0;
      width: 20px;
      height: 20px;
      background: radial-gradient(circle, rgba(212, 175, 55, 0.8) 0%, rgba(255, 215, 0, 0.6) 50%, transparent 70%);
      border-radius: 50%;
      pointer-events: none;
      z-index: 9999;
      transition: transform 0.1s ease;
      mix-blend-mode: difference;
    }

    .custom-cursor-dot {
      position: fixed;
      top: 0;
      left: 0;
      width: 4px;
      height: 4px;
      background: #FFD700;
      border-radius: 50%;
      pointer-events: none;
      z-index: 10000;
      transition: transform 0.05s ease;
    }

    /* Cursor hover effects */
    .cursor-hover {
      transform: scale(2);
      background: radial-gradient(circle, rgba(212, 175, 55, 1) 0%, rgba(255, 215, 0, 0.8) 50%, transparent 70%);
    }

    .cursor-click {
      transform: scale(0.8);
      background: radial-gradient(circle, rgba(255, 215, 0, 1) 0%, rgba(212, 175, 55, 0.9) 50%, transparent 70%);
    }

    /* Cursor for different elements */
    a, button, .luxury-btn, .magnetic, .hover-lift {
      cursor: none !important;
    }

    input, textarea, select {
      cursor: none !important;
    }

    /* Text cursor for inputs */
    .cursor-text {
      width: 2px;
      height: 20px;
      background: linear-gradient(180deg, #D4AF37 0%, #FFD700 100%);
      border-radius: 1px;
    }

    /* Loading cursor */
    .cursor-loading {
      width: 24px;
      height: 24px;
      border: 2px solid rgba(212, 175, 55, 0.3);
      border-top: 2px solid #D4AF37;
      border-radius: 50%;
      animation: spin 1s linear infinite;
    }

    @keyframes spin {
      0% { transform: rotate(0deg); }
      100% { transform: rotate(360deg); }
    }

    /* Cursor trail */
    .cursor-trail {
      position: fixed;
      width: 6px;
      height: 6px;
      background: rgba(212, 175, 55, 0.6);
      border-radius: 50%;
      pointer-events: none;
      z-index: 9998;
      transition: opacity 0.3s ease;
    }

    /* Special cursor effects for different elements */
    .cursor-gallery {
      width: 30px;
      height: 30px;
      background: radial-gradient(circle, rgba(212, 175, 55, 0.9) 0%, rgba(255, 215, 0, 0.7) 50%, transparent 70%);
      border: 2px solid rgba(255, 215, 0, 0.8);
    }

    .cursor-contact {
      width: 25px;
      height: 25px;
      background: radial-gradient(circle, rgba(34, 197, 94, 0.8) 0%, rgba(16, 185, 129, 0.6) 50%, transparent 70%);
      border: 2px solid rgba(34, 197, 94, 0.8);
    }

    .cursor-whatsapp {
      width: 28px;
      height: 28px;
      background: radial-gradient(circle, rgba(37, 211, 102, 0.9) 0%, rgba(34, 197, 94, 0.7) 50%, transparent 70%);
      border: 2px solid rgba(37, 211, 102, 0.9);
    }

    /* Cursor glow effect */
    .cursor-glow {
      box-shadow:
        0 0 20px rgba(212, 175, 55, 0.6),
        0 0 40px rgba(212, 175, 55, 0.4),
        0 0 60px rgba(212, 175, 55, 0.2);
    }

    /* Hide default cursor on all elements */
    *, *:before, *:after {
      cursor: none !important;
    }

    /* Smooth cursor movement */
    .custom-cursor, .custom-cursor-dot {
      will-change: transform;
    }
  </style>

  <link rel="icon" href="/favicon.ico" />
</head>
<body class="text-gray-900 font-sans bg-white">

<!-- Custom Cursor -->
<div class="custom-cursor" id="customCursor"></div>
<div class="custom-cursor-dot" id="customCursorDot"></div>

<!-- Scroll Progress Indicator -->
<div class="fixed top-0 left-0 w-full h-1 bg-gray-900 z-50">
  <div class="h-full bg-gradient-to-r from-yellow-400 via-yellow-500 to-yellow-600 transition-all duration-300 ease-out" id="scrollProgress" style="width: 0%;"></div>
</div>

<!-- Navigation -->
<nav class="fixed top-0 w-full z-50 navbar-glass transition-all duration-500 transform" id="navbar">
  <div class="max-w-7xl mx-auto px-6 lg:px-8">
    <div class="flex justify-between items-center h-20">
      <div class="flex items-center">
        <div class="text-2xl font-playfair font-bold magnetic glow-effect">
          <span class="gradient-text">Green Alley</span>
        </div>
      </div>

      <div class="hidden md:flex items-center space-x-8">
        <a href="#home" class="nav-link text-white hover:text-yellow-400 transition-all duration-300 font-medium relative overflow-hidden">
          <span class="relative z-10">Home</span>
          <div class="absolute bottom-0 left-0 w-0 h-0.5 bg-gradient-to-r from-yellow-400 to-yellow-600 transition-all duration-300 hover:w-full"></div>
        </a>
        <a href="#about" class="nav-link text-white hover:text-yellow-400 transition-all duration-300 font-medium relative overflow-hidden">
          <span class="relative z-10">About</span>
          <div class="absolute bottom-0 left-0 w-0 h-0.5 bg-gradient-to-r from-yellow-400 to-yellow-600 transition-all duration-300 hover:w-full"></div>
        </a>
        <a href="#gallery" class="nav-link text-white hover:text-yellow-400 transition-all duration-300 font-medium relative overflow-hidden">
          <span class="relative z-10">Gallery</span>
          <div class="absolute bottom-0 left-0 w-0 h-0.5 bg-gradient-to-r from-yellow-400 to-yellow-600 transition-all duration-300 hover:w-full"></div>
        </a>
        <a href="#amenities" class="nav-link text-white hover:text-yellow-400 transition-all duration-300 font-medium relative overflow-hidden">
          <span class="relative z-10">Amenities</span>
          <div class="absolute bottom-0 left-0 w-0 h-0.5 bg-gradient-to-r from-yellow-400 to-yellow-600 transition-all duration-300 hover:w-full"></div>
        </a>
        <a href="#location" class="nav-link text-white hover:text-yellow-400 transition-all duration-300 font-medium relative overflow-hidden">
          <span class="relative z-10">Location</span>
          <div class="absolute bottom-0 left-0 w-0 h-0.5 bg-gradient-to-r from-yellow-400 to-yellow-600 transition-all duration-300 hover:w-full"></div>
        </a>
        <a href="#contact" class="nav-link text-white hover:text-yellow-400 transition-all duration-300 font-medium relative overflow-hidden">
          <span class="relative z-10">Contact</span>
          <div class="absolute bottom-0 left-0 w-0 h-0.5 bg-gradient-to-r from-yellow-400 to-yellow-600 transition-all duration-300 hover:w-full"></div>
        </a>
        <a href="#enquiry" class="luxury-btn text-sm morph-btn magnetic">Book Visit</a>
      </div>

      <div class="md:hidden">
        <button id="mobile-menu-btn" class="text-white focus:outline-none magnetic">
          <div class="hamburger-menu">
            <span class="line line1"></span>
            <span class="line line2"></span>
            <span class="line line3"></span>
          </div>
        </button>
      </div>
    </div>
  </div>

  <!-- Mobile Menu -->
  <div id="mobile-menu" class="md:hidden hidden bg-black bg-opacity-95 backdrop-blur-lg transform transition-all duration-500 translate-y-full">
    <div class="px-6 py-4 space-y-4">
      <a href="#home" class="block text-white hover:text-yellow-400 transition-all duration-300 font-medium transform hover:translateX-2">Home</a>
      <a href="#about" class="block text-white hover:text-yellow-400 transition-all duration-300 font-medium transform hover:translateX-2">About</a>
      <a href="#gallery" class="block text-white hover:text-yellow-400 transition-all duration-300 font-medium transform hover:translateX-2">Gallery</a>
      <a href="#amenities" class="block text-white hover:text-yellow-400 transition-all duration-300 font-medium transform hover:translateX-2">Amenities</a>
      <a href="#location" class="block text-white hover:text-yellow-400 transition-all duration-300 font-medium transform hover:translateX-2">Location</a>
      <a href="#contact" class="block text-white hover:text-yellow-400 transition-all duration-300 font-medium transform hover:translateX-2">Contact</a>
      <a href="#enquiry" class="luxury-btn text-sm inline-block mt-4 morph-btn">Book Visit</a>
    </div>
  </div>
</nav>

<style>
.nav-link:hover .absolute {
  width: 100%;
}

.hamburger-menu {
  width: 24px;
  height: 18px;
  position: relative;
  cursor: pointer;
}

.line {
  display: block;
  height: 2px;
  width: 100%;
  background: white;
  position: absolute;
  transition: all 0.3s ease;
}

.line1 { top: 0; }
.line2 { top: 50%; transform: translateY(-50%); }
.line3 { bottom: 0; }

.hamburger-menu.active .line1 {
  transform: rotate(45deg);
  top: 50%;
  margin-top: -1px;
}

.hamburger-menu.active .line2 {
  opacity: 0;
}

.hamburger-menu.active .line3 {
  transform: rotate(-45deg);
  bottom: 50%;
  margin-bottom: -1px;
}
</style>

<!-- Hero Section -->
<section id="home" class="relative h-screen w-full overflow-hidden particle-bg">
  <!-- Background Video/Image -->
  <div class="absolute inset-0 z-0">
    <div class="w-full h-full bg-gradient-to-br from-gray-900 via-green-900 to-black"></div>
    <!-- Placeholder for video - you can add actual video here -->
    <div class="absolute inset-0 bg-cover bg-center bg-no-repeat opacity-40 parallax"
         style="background-image: url('https://images.unsplash.com/photo-1600596542815-ffad4c1539a9?ixlib=rb-4.0.3&auto=format&fit=crop&w=2070&q=80');" data-speed="0.5">
    </div>
  </div>

  <!-- Particle Effects -->
  <div class="absolute inset-0 z-5">
    <div class="particle" style="left: 10%; animation-delay: 0s;"></div>
    <div class="particle" style="left: 20%; animation-delay: 2s;"></div>
    <div class="particle" style="left: 30%; animation-delay: 4s;"></div>
    <div class="particle" style="left: 40%; animation-delay: 6s;"></div>
    <div class="particle" style="left: 50%; animation-delay: 8s;"></div>
    <div class="particle" style="left: 60%; animation-delay: 10s;"></div>
    <div class="particle" style="left: 70%; animation-delay: 12s;"></div>
    <div class="particle" style="left: 80%; animation-delay: 14s;"></div>
    <div class="particle" style="left: 90%; animation-delay: 16s;"></div>
  </div>

  <!-- Hero Overlay -->
  <div class="absolute inset-0 hero-overlay z-10 liquid-bg"></div>

  <!-- Hero Content -->
  <div class="relative z-20 flex flex-col justify-center items-center text-center h-full px-6 text-white">
    <div class="max-w-5xl mx-auto" data-aos="fade-up" data-aos-duration="1000">
      <h1 class="text-5xl md:text-7xl lg:text-8xl font-playfair font-bold mb-6 leading-tight">
        <span class="inline-block" data-aos="slide-down" data-aos-delay="200">Welcome to</span> <br>
        <span class="gradient-text inline-block glow-effect" data-aos="zoom-in" data-aos-delay="600">Green Alley</span>
      </h1>
      <p class="text-xl md:text-2xl lg:text-3xl mb-8 max-w-3xl mx-auto font-light leading-relaxed" data-aos="fade-up" data-aos-delay="800">
        Experience Unparalleled <span class="text-yellow-400 font-semibold">Luxury Living</span> in the Green Heart of Pune
      </p>
      <div class="flex flex-col md:flex-row justify-center items-center space-y-4 md:space-y-0 md:space-x-6" data-aos="fade-up" data-aos-delay="1000">
        <a href="#enquiry" class="luxury-btn morph-btn magnetic scale-in">
          <i class="fas fa-calendar-check mr-2"></i>
          Book Site Visit
        </a>
        <a href="#gallery" class="glass-effect px-8 py-4 rounded-full text-white hover:bg-white hover:text-black transition-all duration-500 font-semibold tilt-effect magnetic">
          <i class="fas fa-play mr-2"></i>
          View Gallery
        </a>
      </div>
    </div>

    <!-- Scroll Indicator -->
    <div class="absolute bottom-8 left-1/2 transform -translate-x-1/2 text-white animate-bounce magnetic" data-aos="fade-up" data-aos-delay="1200">
      <div class="flex flex-col items-center">
        <span class="text-sm mb-2 opacity-75">Scroll Down</span>
        <i class="fas fa-chevron-down text-2xl glow-effect"></i>
      </div>
    </div>
  </div>

  <!-- Enhanced Floating Elements -->
  <div class="absolute top-20 left-10 w-20 h-20 bg-yellow-400 rounded-full opacity-20 floating glow-effect"></div>
  <div class="absolute bottom-20 right-10 w-16 h-16 bg-green-400 rounded-full opacity-20 floating glow-effect" style="animation-delay: 3s;"></div>
  <div class="absolute top-1/2 right-20 w-12 h-12 bg-yellow-300 rounded-full opacity-15 floating" style="animation-delay: 5s;"></div>
  <div class="absolute bottom-1/3 left-20 w-8 h-8 bg-green-300 rounded-full opacity-15 floating" style="animation-delay: 7s;"></div>

  <!-- Geometric Shapes -->
  <div class="absolute top-1/4 left-1/4 w-32 h-32 border-2 border-yellow-400 opacity-10 rotate-45 floating"></div>
  <div class="absolute bottom-1/4 right-1/4 w-24 h-24 border-2 border-green-400 opacity-10 rounded-full floating" style="animation-delay: 4s;"></div>
</section>

<!-- About Section -->
<section id="about" class="py-20 px-6 md:px-16 bg-white relative overflow-hidden">
  <div class="max-w-7xl mx-auto">
    <div class="text-center mb-16" data-aos="fade-up">
      <h2 class="text-5xl md:text-6xl font-playfair font-bold mb-6 text-gray-900">
        Prime Location. <span class="gradient-text">Peaceful Living.</span> Premium Lifestyle.
      </h2>
      <div class="w-24 h-1 mx-auto mb-8" style="background: linear-gradient(135deg, #D4AF37 0%, #FFD700 50%, #B8860B 100%);"></div>
      <p class="max-w-4xl mx-auto text-xl text-gray-600 leading-relaxed">
        Nestled beside lush army greens in Pimple Nilakh, Green Alley offers 4 BHK designer villas with uninterrupted views, exceptional connectivity, and sophisticated living that redefines luxury.
      </p>
    </div>

    <div class="grid lg:grid-cols-2 gap-16 items-center">
      <div data-aos="fade-right" data-aos-duration="1000">
        <div class="relative group">
          <div class="absolute inset-0 bg-gradient-to-r from-yellow-400 to-yellow-600 rounded-2xl blur-lg opacity-25 group-hover:opacity-40 transition-opacity duration-500"></div>
          <img src="https://images.unsplash.com/photo-1600607687939-ce8a6c25118c?ixlib=rb-4.0.3&auto=format&fit=crop&w=1000&q=80"
               alt="Luxury Villa"
               class="relative rounded-2xl luxury-shadow w-full h-96 object-cover hover-lift tilt-effect">
          <div class="absolute -bottom-6 -right-6 w-32 h-32 luxury-gradient rounded-2xl flex items-center justify-center glow-effect rotate-in">
            <div class="text-center text-white">
              <div class="text-2xl font-bold counter" data-target="4">0</div>
              <div class="text-sm">BHK Villas</div>
            </div>
          </div>
          <!-- Overlay on hover -->
          <div class="absolute inset-0 bg-black bg-opacity-50 rounded-2xl opacity-0 group-hover:opacity-100 transition-opacity duration-500 flex items-center justify-center">
            <div class="text-white text-center">
              <i class="fas fa-search-plus text-3xl mb-2"></i>
              <p class="text-lg font-semibold">View Details</p>
            </div>
          </div>
        </div>
      </div>

      <div data-aos="fade-left" data-aos-duration="1000">
        <h3 class="text-3xl font-playfair font-bold mb-6 text-gray-900 text-reveal">Why Choose Green Alley?</h3>
        <div class="space-y-6">
          <div class="flex items-start space-x-4 group hover-lift" data-aos="slide-in-right" data-aos-delay="200">
            <div class="w-12 h-12 luxury-gradient rounded-full flex items-center justify-center flex-shrink-0 magnetic glow-effect">
              <i class="fas fa-home text-white transition-transform duration-300 group-hover:scale-110"></i>
            </div>
            <div class="flex-1">
              <h4 class="text-xl font-semibold mb-2 text-gray-900 group-hover:text-yellow-600 transition-colors duration-300">Designer Villas</h4>
              <p class="text-gray-600 group-hover:text-gray-700 transition-colors duration-300">Spacious 4 BHK villas with contemporary architecture and premium finishes</p>
            </div>
          </div>

          <div class="flex items-start space-x-4 group hover-lift" data-aos="slide-in-right" data-aos-delay="400">
            <div class="w-12 h-12 luxury-gradient rounded-full flex items-center justify-center flex-shrink-0 magnetic glow-effect">
              <i class="fas fa-tree text-white transition-transform duration-300 group-hover:scale-110"></i>
            </div>
            <div class="flex-1">
              <h4 class="text-xl font-semibold mb-2 text-gray-900 group-hover:text-yellow-600 transition-colors duration-300">Green Views</h4>
              <p class="text-gray-600 group-hover:text-gray-700 transition-colors duration-300">Facing army green zone with guaranteed nature views for lifetime</p>
            </div>
          </div>

          <div class="flex items-start space-x-4 group hover-lift" data-aos="slide-in-right" data-aos-delay="600">
            <div class="w-12 h-12 luxury-gradient rounded-full flex items-center justify-center flex-shrink-0 magnetic glow-effect">
              <i class="fas fa-shield-alt text-white transition-transform duration-300 group-hover:scale-110"></i>
            </div>
            <div class="flex-1">
              <h4 class="text-xl font-semibold mb-2 text-gray-900 group-hover:text-yellow-600 transition-colors duration-300">Premium Security</h4>
              <p class="text-gray-600 group-hover:text-gray-700 transition-colors duration-300">24x7 security with CCTV surveillance and gated community</p>
            </div>
          </div>

          <div class="flex items-start space-x-4 group hover-lift" data-aos="slide-in-right" data-aos-delay="800">
            <div class="w-12 h-12 luxury-gradient rounded-full flex items-center justify-center flex-shrink-0 magnetic glow-effect">
              <i class="fas fa-wifi text-white transition-transform duration-300 group-hover:scale-110"></i>
            </div>
            <div class="flex-1">
              <h4 class="text-xl font-semibold mb-2 text-gray-900 group-hover:text-yellow-600 transition-colors duration-300">Smart Ready</h4>
              <p class="text-gray-600 group-hover:text-gray-700 transition-colors duration-300">Modern automation-ready infrastructure for smart living</p>
            </div>
          </div>
        </div>

        <!-- Progress Bars -->
        <div class="mt-8 space-y-4" data-aos="fade-up" data-aos-delay="1000">
          <div class="progress-item">
            <div class="flex justify-between mb-2">
              <span class="text-sm font-semibold text-gray-700">Luxury Features</span>
              <span class="text-sm text-gray-600">95%</span>
            </div>
            <div class="w-full bg-gray-200 rounded-full h-2">
              <div class="bg-gradient-to-r from-yellow-400 to-yellow-600 h-2 rounded-full progress-bar" data-width="95"></div>
            </div>
          </div>
          <div class="progress-item">
            <div class="flex justify-between mb-2">
              <span class="text-sm font-semibold text-gray-700">Location Advantage</span>
              <span class="text-sm text-gray-600">90%</span>
            </div>
            <div class="w-full bg-gray-200 rounded-full h-2">
              <div class="bg-gradient-to-r from-green-400 to-green-600 h-2 rounded-full progress-bar" data-width="90"></div>
            </div>
          </div>
          <div class="progress-item">
            <div class="flex justify-between mb-2">
              <span class="text-sm font-semibold text-gray-700">Investment Value</span>
              <span class="text-sm text-gray-600">98%</span>
            </div>
            <div class="w-full bg-gray-200 rounded-full h-2">
              <div class="bg-gradient-to-r from-blue-400 to-blue-600 h-2 rounded-full progress-bar" data-width="98"></div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Background Elements -->
  <div class="absolute top-0 right-0 w-64 h-64 bg-gradient-to-br from-yellow-200 to-yellow-400 rounded-full opacity-10 -translate-y-32 translate-x-32"></div>
  <div class="absolute bottom-0 left-0 w-48 h-48 bg-gradient-to-tr from-green-200 to-green-400 rounded-full opacity-10 translate-y-24 -translate-x-24"></div>
</section>

<!-- Location Section -->
<section id="location" class="py-20 px-6 md:px-16 bg-white relative overflow-hidden">
  <div class="max-w-7xl mx-auto">
    <div class="text-center mb-16" data-aos="fade-up">
      <h2 class="text-5xl md:text-6xl font-playfair font-bold mb-6 text-gray-900">
        Prime <span class="gradient-text">Location</span>
      </h2>
      <div class="w-24 h-1 mx-auto mb-8" style="background: linear-gradient(135deg, #D4AF37 0%, #FFD700 50%, #B8860B 100%);"></div>
      <p class="max-w-3xl mx-auto text-xl text-gray-600 leading-relaxed">
        Strategically located in Pimple Nilakh with excellent connectivity to all major landmarks
      </p>
    </div>

    <div class="grid lg:grid-cols-2 gap-16 items-start mb-16">
      <!-- Location Highlights -->
      <div data-aos="fade-right">
        <div class="grid md:grid-cols-2 gap-8">
          <!-- Hospitals -->
          <div class="bg-gradient-to-br from-red-50 to-red-100 rounded-2xl p-6 hover-lift">
            <div class="flex items-center mb-4">
              <div class="w-12 h-12 bg-red-500 rounded-xl flex items-center justify-center mr-4">
                <i class="fas fa-hospital text-white"></i>
              </div>
              <h3 class="text-xl font-bold text-gray-900">Healthcare</h3>
            </div>
            <ul class="space-y-3 text-gray-700">
              <li class="flex items-center">
                <i class="fas fa-map-marker-alt text-red-500 mr-2"></i>
                Jupiter Hospital – 10 mins
              </li>
              <li class="flex items-center">
                <i class="fas fa-map-marker-alt text-red-500 mr-2"></i>
                Surya Mother & Child – 7 mins
              </li>
              <li class="flex items-center">
                <i class="fas fa-map-marker-alt text-red-500 mr-2"></i>
                Lifepoint Wakad – 10 mins
              </li>
              <li class="flex items-center">
                <i class="fas fa-map-marker-alt text-red-500 mr-2"></i>
                Aditya Birla Hospital – 15 mins
              </li>
            </ul>
          </div>

          <!-- Education -->
          <div class="bg-gradient-to-br from-blue-50 to-blue-100 rounded-2xl p-6 hover-lift">
            <div class="flex items-center mb-4">
              <div class="w-12 h-12 bg-blue-500 rounded-xl flex items-center justify-center mr-4">
                <i class="fas fa-graduation-cap text-white"></i>
              </div>
              <h3 class="text-xl font-bold text-gray-900">Education</h3>
            </div>
            <ul class="space-y-3 text-gray-700">
              <li class="flex items-center">
                <i class="fas fa-map-marker-alt text-blue-500 mr-2"></i>
                Wisdom World Wakad – 10 mins
              </li>
              <li class="flex items-center">
                <i class="fas fa-map-marker-alt text-blue-500 mr-2"></i>
                Indira National – 15 mins
              </li>
              <li class="flex items-center">
                <i class="fas fa-map-marker-alt text-blue-500 mr-2"></i>
                MITCON Balewadi – 7 mins
              </li>
              <li class="flex items-center">
                <i class="fas fa-map-marker-alt text-blue-500 mr-2"></i>
                International Schools Nearby
              </li>
            </ul>
          </div>

          <!-- Shopping -->
          <div class="bg-gradient-to-br from-purple-50 to-purple-100 rounded-2xl p-6 hover-lift">
            <div class="flex items-center mb-4">
              <div class="w-12 h-12 bg-purple-500 rounded-xl flex items-center justify-center mr-4">
                <i class="fas fa-shopping-bag text-white"></i>
              </div>
              <h3 class="text-xl font-bold text-gray-900">Shopping</h3>
            </div>
            <ul class="space-y-3 text-gray-700">
              <li class="flex items-center">
                <i class="fas fa-map-marker-alt text-purple-500 mr-2"></i>
                Westend Mall Aundh – 10 mins
              </li>
              <li class="flex items-center">
                <i class="fas fa-map-marker-alt text-purple-500 mr-2"></i>
                Phoenix Marketcity – 12 mins
              </li>
              <li class="flex items-center">
                <i class="fas fa-map-marker-alt text-purple-500 mr-2"></i>
                Balewadi High Street – 10 mins
              </li>
              <li class="flex items-center">
                <i class="fas fa-map-marker-alt text-purple-500 mr-2"></i>
                Local Markets Nearby
              </li>
            </ul>
          </div>

          <!-- Transport -->
          <div class="bg-gradient-to-br from-green-50 to-green-100 rounded-2xl p-6 hover-lift">
            <div class="flex items-center mb-4">
              <div class="w-12 h-12 bg-green-500 rounded-xl flex items-center justify-center mr-4">
                <i class="fas fa-subway text-white"></i>
              </div>
              <h3 class="text-xl font-bold text-gray-900">Transport</h3>
            </div>
            <ul class="space-y-3 text-gray-700">
              <li class="flex items-center">
                <i class="fas fa-map-marker-alt text-green-500 mr-2"></i>
                Metro Line 3 – 1.5 km
              </li>
              <li class="flex items-center">
                <i class="fas fa-map-marker-alt text-green-500 mr-2"></i>
                Mumbai-Pune Expressway – 10 mins
              </li>
              <li class="flex items-center">
                <i class="fas fa-map-marker-alt text-green-500 mr-2"></i>
                BRT Corridor – Nearby
              </li>
              <li class="flex items-center">
                <i class="fas fa-map-marker-alt text-green-500 mr-2"></i>
                Airport – 45 mins
              </li>
            </ul>
          </div>
        </div>
      </div>

      <!-- Interactive Map -->
      <div data-aos="fade-left">
        <div class="bg-white rounded-2xl luxury-shadow overflow-hidden">
          <div class="p-6 bg-gradient-to-r from-gray-900 to-gray-800">
            <h3 class="text-2xl font-bold text-white mb-2">Interactive Location Map</h3>
            <p class="text-gray-300">Explore the neighborhood and nearby amenities</p>
          </div>
          <div class="relative">
            <iframe class="w-full h-96"
              src="https://www.google.com/maps/embed?pb=!1m18!1m12!1m3!1d3782.0362663249555!2d73.7855!3d18.5669!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0x3bc2b94e3e121f7f%3A0x7ad58f3!2sPimple%20Nilakh%2C%20Pune%2C%20Maharashtra!5e0!3m2!1sen!2sin!4v1719821234567"
              allowfullscreen="" loading="lazy"></iframe>
            <div class="absolute top-4 left-4 bg-white rounded-lg p-3 luxury-shadow">
              <div class="flex items-center">
                <div class="w-3 h-3 bg-red-500 rounded-full mr-2"></div>
                <span class="text-sm font-semibold text-gray-900">Green Alley</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Background Elements -->
  <div class="absolute top-0 left-0 w-64 h-64 bg-gradient-to-br from-yellow-200 to-yellow-400 rounded-full opacity-10 -translate-y-32 -translate-x-32"></div>
  <div class="absolute bottom-0 right-0 w-48 h-48 bg-gradient-to-tl from-green-200 to-green-400 rounded-full opacity-10 translate-y-24 translate-x-24"></div>
</section>

<!-- Amenities Section -->
<section id="amenities" class="py-20 px-6 md:px-16 bg-gray-50 relative overflow-hidden">
  <div class="max-w-7xl mx-auto">
    <div class="text-center mb-16" data-aos="fade-up">
      <h2 class="text-5xl md:text-6xl font-playfair font-bold mb-6 text-gray-900">
        Luxury <span class="gradient-text">Amenities</span>
      </h2>
      <div class="w-24 h-1 mx-auto mb-8" style="background: linear-gradient(135deg, #D4AF37 0%, #FFD700 50%, #B8860B 100%);"></div>
      <p class="max-w-3xl mx-auto text-xl text-gray-600 leading-relaxed">
        Experience world-class amenities designed for the discerning homeowner
      </p>
    </div>

    <div class="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
      <!-- Amenity Card 1 -->
      <div class="group bg-white rounded-2xl p-8 luxury-shadow hover-lift tilt-effect magnetic" data-aos="flip-left" data-aos-delay="100">
        <div class="relative">
          <div class="absolute inset-0 bg-gradient-to-r from-yellow-400 to-yellow-600 rounded-2xl blur-md opacity-0 group-hover:opacity-30 transition-opacity duration-500"></div>
          <div class="relative w-16 h-16 luxury-gradient rounded-2xl flex items-center justify-center mb-6 group-hover:scale-110 transition-transform duration-300 glow-effect rotate-in">
            <i class="fas fa-home text-white text-2xl group-hover:rotate-12 transition-transform duration-300"></i>
          </div>
        </div>
        <h3 class="text-2xl font-bold mb-4 text-gray-900 group-hover:text-yellow-600 transition-colors duration-300">4 BHK Designer Villas</h3>
        <p class="text-gray-600 leading-relaxed group-hover:text-gray-700 transition-colors duration-300">Spacious, airy interiors with luxury finishes and contemporary architecture</p>
        <div class="mt-4 opacity-0 group-hover:opacity-100 transition-opacity duration-300">
          <div class="w-full bg-gray-200 rounded-full h-1">
            <div class="bg-gradient-to-r from-yellow-400 to-yellow-600 h-1 rounded-full w-0 group-hover:w-full transition-all duration-1000"></div>
          </div>
        </div>
      </div>

      <!-- Amenity Card 2 -->
      <div class="group bg-white rounded-2xl p-8 luxury-shadow hover-lift tilt-effect magnetic" data-aos="flip-left" data-aos-delay="200">
        <div class="relative">
          <div class="absolute inset-0 bg-gradient-to-r from-green-400 to-green-600 rounded-2xl blur-md opacity-0 group-hover:opacity-30 transition-opacity duration-500"></div>
          <div class="relative w-16 h-16 luxury-gradient rounded-2xl flex items-center justify-center mb-6 group-hover:scale-110 transition-transform duration-300 glow-effect rotate-in">
            <i class="fas fa-tree text-white text-2xl group-hover:rotate-12 transition-transform duration-300"></i>
          </div>
        </div>
        <h3 class="text-2xl font-bold mb-4 text-gray-900 group-hover:text-yellow-600 transition-colors duration-300">Army Green Zone Views</h3>
        <p class="text-gray-600 leading-relaxed group-hover:text-gray-700 transition-colors duration-300">Guaranteed green views for life with unobstructed nature vistas</p>
        <div class="mt-4 opacity-0 group-hover:opacity-100 transition-opacity duration-300">
          <div class="w-full bg-gray-200 rounded-full h-1">
            <div class="bg-gradient-to-r from-green-400 to-green-600 h-1 rounded-full w-0 group-hover:w-full transition-all duration-1000"></div>
          </div>
        </div>
      </div>

      <!-- Amenity Card 3 -->
      <div class="group bg-white rounded-2xl p-8 luxury-shadow hover-lift tilt-effect magnetic" data-aos="flip-left" data-aos-delay="300">
        <div class="relative">
          <div class="absolute inset-0 bg-gradient-to-r from-blue-400 to-blue-600 rounded-2xl blur-md opacity-0 group-hover:opacity-30 transition-opacity duration-500"></div>
          <div class="relative w-16 h-16 luxury-gradient rounded-2xl flex items-center justify-center mb-6 group-hover:scale-110 transition-transform duration-300 glow-effect rotate-in">
            <i class="fas fa-car text-white text-2xl group-hover:rotate-12 transition-transform duration-300"></i>
          </div>
        </div>
        <h3 class="text-2xl font-bold mb-4 text-gray-900 group-hover:text-yellow-600 transition-colors duration-300">Private Parking</h3>
        <p class="text-gray-600 leading-relaxed group-hover:text-gray-700 transition-colors duration-300">Dedicated covered car park per unit with additional guest parking</p>
        <div class="mt-4 opacity-0 group-hover:opacity-100 transition-opacity duration-300">
          <div class="w-full bg-gray-200 rounded-full h-1">
            <div class="bg-gradient-to-r from-blue-400 to-blue-600 h-1 rounded-full w-0 group-hover:w-full transition-all duration-1000"></div>
          </div>
        </div>
      </div>

      <!-- Amenity Card 4 -->
      <div class="group bg-white rounded-2xl p-8 luxury-shadow hover-lift tilt-effect magnetic" data-aos="flip-left" data-aos-delay="400">
        <div class="relative">
          <div class="absolute inset-0 bg-gradient-to-r from-purple-400 to-purple-600 rounded-2xl blur-md opacity-0 group-hover:opacity-30 transition-opacity duration-500"></div>
          <div class="relative w-16 h-16 luxury-gradient rounded-2xl flex items-center justify-center mb-6 group-hover:scale-110 transition-transform duration-300 glow-effect rotate-in">
            <i class="fas fa-seedling text-white text-2xl group-hover:rotate-12 transition-transform duration-300"></i>
          </div>
        </div>
        <h3 class="text-2xl font-bold mb-4 text-gray-900 group-hover:text-yellow-600 transition-colors duration-300">Terrace Gardens</h3>
        <p class="text-gray-600 leading-relaxed group-hover:text-gray-700 transition-colors duration-300">Private terrace gardens with panoramic views of nature</p>
        <div class="mt-4 opacity-0 group-hover:opacity-100 transition-opacity duration-300">
          <div class="w-full bg-gray-200 rounded-full h-1">
            <div class="bg-gradient-to-r from-purple-400 to-purple-600 h-1 rounded-full w-0 group-hover:w-full transition-all duration-1000"></div>
          </div>
        </div>
      </div>

      <!-- Amenity Card 5 -->
      <div class="group bg-white rounded-2xl p-8 luxury-shadow hover-lift tilt-effect magnetic" data-aos="flip-left" data-aos-delay="500">
        <div class="relative">
          <div class="absolute inset-0 bg-gradient-to-r from-red-400 to-red-600 rounded-2xl blur-md opacity-0 group-hover:opacity-30 transition-opacity duration-500"></div>
          <div class="relative w-16 h-16 luxury-gradient rounded-2xl flex items-center justify-center mb-6 group-hover:scale-110 transition-transform duration-300 glow-effect rotate-in">
            <i class="fas fa-shield-alt text-white text-2xl group-hover:rotate-12 transition-transform duration-300"></i>
          </div>
        </div>
        <h3 class="text-2xl font-bold mb-4 text-gray-900 group-hover:text-yellow-600 transition-colors duration-300">Gated & Secure</h3>
        <p class="text-gray-600 leading-relaxed group-hover:text-gray-700 transition-colors duration-300">24x7 security with CCTV surveillance and controlled access</p>
        <div class="mt-4 opacity-0 group-hover:opacity-100 transition-opacity duration-300">
          <div class="w-full bg-gray-200 rounded-full h-1">
            <div class="bg-gradient-to-r from-red-400 to-red-600 h-1 rounded-full w-0 group-hover:w-full transition-all duration-1000"></div>
          </div>
        </div>
      </div>

      <!-- Amenity Card 6 -->
      <div class="group bg-white rounded-2xl p-8 luxury-shadow hover-lift tilt-effect magnetic" data-aos="flip-left" data-aos-delay="600">
        <div class="relative">
          <div class="absolute inset-0 bg-gradient-to-r from-indigo-400 to-indigo-600 rounded-2xl blur-md opacity-0 group-hover:opacity-30 transition-opacity duration-500"></div>
          <div class="relative w-16 h-16 luxury-gradient rounded-2xl flex items-center justify-center mb-6 group-hover:scale-110 transition-transform duration-300 glow-effect rotate-in">
            <i class="fas fa-wifi text-white text-2xl group-hover:rotate-12 transition-transform duration-300"></i>
          </div>
        </div>
        <h3 class="text-2xl font-bold mb-4 text-gray-900 group-hover:text-yellow-600 transition-colors duration-300">Smart Home Ready</h3>
        <p class="text-gray-600 leading-relaxed group-hover:text-gray-700 transition-colors duration-300">Modern automation-ready infrastructure for intelligent living</p>
        <div class="mt-4 opacity-0 group-hover:opacity-100 transition-opacity duration-300">
          <div class="w-full bg-gray-200 rounded-full h-1">
            <div class="bg-gradient-to-r from-indigo-400 to-indigo-600 h-1 rounded-full w-0 group-hover:w-full transition-all duration-1000"></div>
          </div>
        </div>
      </div>

      <!-- Amenity Card 7 -->
      <div class="group bg-white rounded-2xl p-8 luxury-shadow hover-lift tilt-effect magnetic" data-aos="flip-left" data-aos-delay="700">
        <div class="relative">
          <div class="absolute inset-0 bg-gradient-to-r from-cyan-400 to-cyan-600 rounded-2xl blur-md opacity-0 group-hover:opacity-30 transition-opacity duration-500"></div>
          <div class="relative w-16 h-16 luxury-gradient rounded-2xl flex items-center justify-center mb-6 group-hover:scale-110 transition-transform duration-300 glow-effect rotate-in">
            <i class="fas fa-swimming-pool text-white text-2xl group-hover:rotate-12 transition-transform duration-300"></i>
          </div>
        </div>
        <h3 class="text-2xl font-bold mb-4 text-gray-900 group-hover:text-yellow-600 transition-colors duration-300">Swimming Pool</h3>
        <p class="text-gray-600 leading-relaxed group-hover:text-gray-700 transition-colors duration-300">Resort-style swimming pool with deck area for relaxation</p>
        <div class="mt-4 opacity-0 group-hover:opacity-100 transition-opacity duration-300">
          <div class="w-full bg-gray-200 rounded-full h-1">
            <div class="bg-gradient-to-r from-cyan-400 to-cyan-600 h-1 rounded-full w-0 group-hover:w-full transition-all duration-1000"></div>
          </div>
        </div>
      </div>

      <!-- Amenity Card 8 -->
      <div class="group bg-white rounded-2xl p-8 luxury-shadow hover-lift tilt-effect magnetic" data-aos="flip-left" data-aos-delay="800">
        <div class="relative">
          <div class="absolute inset-0 bg-gradient-to-r from-orange-400 to-orange-600 rounded-2xl blur-md opacity-0 group-hover:opacity-30 transition-opacity duration-500"></div>
          <div class="relative w-16 h-16 luxury-gradient rounded-2xl flex items-center justify-center mb-6 group-hover:scale-110 transition-transform duration-300 glow-effect rotate-in">
            <i class="fas fa-dumbbell text-white text-2xl group-hover:rotate-12 transition-transform duration-300"></i>
          </div>
        </div>
        <h3 class="text-2xl font-bold mb-4 text-gray-900 group-hover:text-yellow-600 transition-colors duration-300">Fitness Center</h3>
        <p class="text-gray-600 leading-relaxed group-hover:text-gray-700 transition-colors duration-300">Fully equipped gymnasium with modern fitness equipment</p>
        <div class="mt-4 opacity-0 group-hover:opacity-100 transition-opacity duration-300">
          <div class="w-full bg-gray-200 rounded-full h-1">
            <div class="bg-gradient-to-r from-orange-400 to-orange-600 h-1 rounded-full w-0 group-hover:w-full transition-all duration-1000"></div>
          </div>
        </div>
      </div>

      <!-- Amenity Card 9 -->
      <div class="group bg-white rounded-2xl p-8 luxury-shadow hover-lift tilt-effect magnetic" data-aos="flip-left" data-aos-delay="900">
        <div class="relative">
          <div class="absolute inset-0 bg-gradient-to-r from-pink-400 to-pink-600 rounded-2xl blur-md opacity-0 group-hover:opacity-30 transition-opacity duration-500"></div>
          <div class="relative w-16 h-16 luxury-gradient rounded-2xl flex items-center justify-center mb-6 group-hover:scale-110 transition-transform duration-300 glow-effect rotate-in">
            <i class="fas fa-users text-white text-2xl group-hover:rotate-12 transition-transform duration-300"></i>
          </div>
        </div>
        <h3 class="text-2xl font-bold mb-4 text-gray-900 group-hover:text-yellow-600 transition-colors duration-300">Community Hall</h3>
        <p class="text-gray-600 leading-relaxed group-hover:text-gray-700 transition-colors duration-300">Elegant community hall for events and social gatherings</p>
        <div class="mt-4 opacity-0 group-hover:opacity-100 transition-opacity duration-300">
          <div class="w-full bg-gray-200 rounded-full h-1">
            <div class="bg-gradient-to-r from-pink-400 to-pink-600 h-1 rounded-full w-0 group-hover:w-full transition-all duration-1000"></div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Background Elements -->
  <div class="absolute top-0 right-0 w-96 h-96 bg-gradient-to-bl from-yellow-100 to-transparent rounded-full opacity-30"></div>
  <div class="absolute bottom-0 left-0 w-64 h-64 bg-gradient-to-tr from-green-100 to-transparent rounded-full opacity-30"></div>
</section>

<!-- Gallery Section -->
<section id="gallery" class="py-20 px-6 md:px-16 dark-gradient relative overflow-hidden">
  <div class="max-w-7xl mx-auto">
    <div class="text-center mb-16" data-aos="fade-up">
      <h2 class="text-5xl md:text-6xl font-playfair font-bold mb-6 text-white">
        Luxury <span class="gradient-text">Gallery</span>
      </h2>
      <div class="w-24 h-1 mx-auto mb-8" style="background: linear-gradient(135deg, #D4AF37 0%, #FFD700 50%, #B8860B 100%);"></div>
      <p class="max-w-3xl mx-auto text-xl text-gray-300 leading-relaxed">
        Explore the exquisite design and premium finishes of our luxury villas
      </p>
    </div>

    <!-- Main Gallery Slider -->
    <div class="mb-16" data-aos="fade-up" data-aos-delay="200">
      <div class="swiper gallery-main luxury-shadow rounded-2xl overflow-hidden">
        <div class="swiper-wrapper">
          <div class="swiper-slide">
            <img src="https://images.unsplash.com/photo-1600607687939-ce8a6c25118c?ixlib=rb-4.0.3&auto=format&fit=crop&w=2000&q=80"
                 alt="Luxury Villa Exterior" class="w-full h-96 md:h-[500px] object-cover">
            <div class="absolute bottom-0 left-0 right-0 bg-gradient-to-t from-black to-transparent p-8">
              <h3 class="text-2xl font-bold text-white mb-2">Villa Exterior</h3>
              <p class="text-gray-300">Contemporary architecture with premium finishes</p>
            </div>
          </div>
          <div class="swiper-slide">
            <img src="https://images.unsplash.com/photo-1600566753190-17f0baa2a6c3?ixlib=rb-4.0.3&auto=format&fit=crop&w=2000&q=80"
                 alt="Living Room" class="w-full h-96 md:h-[500px] object-cover">
            <div class="absolute bottom-0 left-0 right-0 bg-gradient-to-t from-black to-transparent p-8">
              <h3 class="text-2xl font-bold text-white mb-2">Spacious Living Room</h3>
              <p class="text-gray-300">Open-plan living with luxury interiors</p>
            </div>
          </div>
          <div class="swiper-slide">
            <img src="https://images.unsplash.com/photo-1600210492486-724fe5c67fb0?ixlib=rb-4.0.3&auto=format&fit=crop&w=2000&q=80"
                 alt="Master Bedroom" class="w-full h-96 md:h-[500px] object-cover">
            <div class="absolute bottom-0 left-0 right-0 bg-gradient-to-t from-black to-transparent p-8">
              <h3 class="text-2xl font-bold text-white mb-2">Master Bedroom</h3>
              <p class="text-gray-300">Elegant bedrooms with premium amenities</p>
            </div>
          </div>
          <div class="swiper-slide">
            <img src="https://images.unsplash.com/photo-1600566753086-00f18fb6b3ea?ixlib=rb-4.0.3&auto=format&fit=crop&w=2000&q=80"
                 alt="Modern Kitchen" class="w-full h-96 md:h-[500px] object-cover">
            <div class="absolute bottom-0 left-0 right-0 bg-gradient-to-t from-black to-transparent p-8">
              <h3 class="text-2xl font-bold text-white mb-2">Designer Kitchen</h3>
              <p class="text-gray-300">State-of-the-art kitchen with premium appliances</p>
            </div>
          </div>
          <div class="swiper-slide">
            <img src="https://images.unsplash.com/photo-1600607687644-c7171b42498b?ixlib=rb-4.0.3&auto=format&fit=crop&w=2000&q=80"
                 alt="Terrace Garden" class="w-full h-96 md:h-[500px] object-cover">
            <div class="absolute bottom-0 left-0 right-0 bg-gradient-to-t from-black to-transparent p-8">
              <h3 class="text-2xl font-bold text-white mb-2">Terrace Garden</h3>
              <p class="text-gray-300">Private terrace with panoramic green views</p>
            </div>
          </div>
        </div>
        <div class="swiper-pagination"></div>
        <div class="swiper-button-next"></div>
        <div class="swiper-button-prev"></div>
      </div>
    </div>

    <!-- Thumbnail Gallery -->
    <div class="grid grid-cols-2 md:grid-cols-4 gap-6" data-aos="fade-up" data-aos-delay="400">
      <div class="group cursor-pointer hover-lift">
        <img src="https://images.unsplash.com/photo-1600607687920-4e2a09cf159d?ixlib=rb-4.0.3&auto=format&fit=crop&w=500&q=80"
             alt="Villa Night View" class="w-full h-48 object-cover rounded-xl luxury-shadow">
        <div class="mt-4 text-center">
          <h4 class="text-lg font-semibold text-white group-hover:text-yellow-400 transition-colors">Night View</h4>
        </div>
      </div>
      <div class="group cursor-pointer hover-lift">
        <img src="https://images.unsplash.com/photo-1600607688969-a5bfcd646154?ixlib=rb-4.0.3&auto=format&fit=crop&w=500&q=80"
             alt="Community Area" class="w-full h-48 object-cover rounded-xl luxury-shadow">
        <div class="mt-4 text-center">
          <h4 class="text-lg font-semibold text-white group-hover:text-yellow-400 transition-colors">Community</h4>
        </div>
      </div>
      <div class="group cursor-pointer hover-lift">
        <img src="https://images.unsplash.com/photo-1600607688960-e095ff8d5c6e?ixlib=rb-4.0.3&auto=format&fit=crop&w=500&q=80"
             alt="Swimming Pool" class="w-full h-48 object-cover rounded-xl luxury-shadow">
        <div class="mt-4 text-center">
          <h4 class="text-lg font-semibold text-white group-hover:text-yellow-400 transition-colors">Pool Area</h4>
        </div>
      </div>
      <div class="group cursor-pointer hover-lift">
        <img src="https://images.unsplash.com/photo-1600607688888-1e4e4e8e8e8e?ixlib=rb-4.0.3&auto=format&fit=crop&w=500&q=80"
             alt="Parking Area" class="w-full h-48 object-cover rounded-xl luxury-shadow">
        <div class="mt-4 text-center">
          <h4 class="text-lg font-semibold text-white group-hover:text-yellow-400 transition-colors">Parking</h4>
        </div>
      </div>
    </div>
  </div>

  <!-- Background Elements -->
  <div class="absolute top-20 left-10 w-32 h-32 bg-yellow-400 rounded-full opacity-10 animate-pulse"></div>
  <div class="absolute bottom-20 right-10 w-24 h-24 bg-green-400 rounded-full opacity-10 animate-pulse" style="animation-delay: 2s;"></div>
</section>

<!-- Contact Section -->
<section id="contact" class="py-20 px-6 md:px-16 dark-gradient relative overflow-hidden">
  <div class="max-w-7xl mx-auto">
    <div class="text-center mb-16" data-aos="fade-up">
      <h2 class="text-5xl md:text-6xl font-playfair font-bold mb-6 text-white">
        Get In <span class="gradient-text">Touch</span>
      </h2>
      <div class="w-24 h-1 mx-auto mb-8" style="background: linear-gradient(135deg, #D4AF37 0%, #FFD700 50%, #B8860B 100%);"></div>
      <p class="max-w-3xl mx-auto text-xl text-gray-300 leading-relaxed">
        Ready to experience luxury living? Contact us today to schedule your site visit
      </p>
    </div>

    <div class="grid lg:grid-cols-2 gap-16 items-start">
      <!-- Contact Form -->
      <div data-aos="fade-right">
        <div class="bg-white rounded-2xl p-8 luxury-shadow">
          <h3 class="text-2xl font-bold mb-6 text-gray-900">Send us a Message</h3>
          <form id="contactForm" class="space-y-6">
            <div class="grid md:grid-cols-2 gap-6">
              <div>
                <label for="firstName" class="block text-sm font-semibold text-gray-700 mb-2">First Name *</label>
                <input type="text" id="firstName" name="firstName" required
                       class="w-full px-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-yellow-500 focus:border-transparent transition-all duration-300">
              </div>
              <div>
                <label for="lastName" class="block text-sm font-semibold text-gray-700 mb-2">Last Name *</label>
                <input type="text" id="lastName" name="lastName" required
                       class="w-full px-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-yellow-500 focus:border-transparent transition-all duration-300">
              </div>
            </div>

            <div>
              <label for="email" class="block text-sm font-semibold text-gray-700 mb-2">Email Address *</label>
              <input type="email" id="email" name="email" required
                     class="w-full px-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-yellow-500 focus:border-transparent transition-all duration-300">
            </div>

            <div>
              <label for="phone" class="block text-sm font-semibold text-gray-700 mb-2">Phone Number *</label>
              <input type="tel" id="phone" name="phone" required
                     class="w-full px-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-yellow-500 focus:border-transparent transition-all duration-300">
            </div>

            <div>
              <label for="interest" class="block text-sm font-semibold text-gray-700 mb-2">I'm Interested In</label>
              <select id="interest" name="interest"
                      class="w-full px-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-yellow-500 focus:border-transparent transition-all duration-300">
                <option value="">Select an option</option>
                <option value="site-visit">Site Visit</option>
                <option value="brochure">Download Brochure</option>
                <option value="pricing">Pricing Information</option>
                <option value="floor-plans">Floor Plans</option>
                <option value="investment">Investment Opportunity</option>
              </select>
            </div>

            <div>
              <label for="message" class="block text-sm font-semibold text-gray-700 mb-2">Message</label>
              <textarea id="message" name="message" rows="4"
                        class="w-full px-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-yellow-500 focus:border-transparent transition-all duration-300"
                        placeholder="Tell us about your requirements..."></textarea>
            </div>

            <button type="submit" class="luxury-btn w-full">
              <i class="fas fa-paper-plane mr-2"></i>
              Send Message
            </button>
          </form>
        </div>
      </div>

      <!-- Contact Information -->
      <div data-aos="fade-left">
        <div class="space-y-8">
          <!-- Quick Contact -->
          <div class="bg-white bg-opacity-10 backdrop-blur-lg rounded-2xl p-8 border border-white border-opacity-20">
            <h3 class="text-2xl font-bold mb-6 text-white">Quick Contact</h3>
            <div class="space-y-6">
              <div class="flex items-center">
                <div class="w-12 h-12 luxury-gradient rounded-xl flex items-center justify-center mr-4">
                  <i class="fas fa-phone text-white"></i>
                </div>
                <div>
                  <p class="text-gray-300 text-sm">Call Us</p>
                  <a href="tel:+917507007875" class="text-white text-lg font-semibold hover:text-yellow-400 transition-colors">+91 7507007875</a>
                </div>
              </div>

              <div class="flex items-center">
                <div class="w-12 h-12 luxury-gradient rounded-xl flex items-center justify-center mr-4">
                  <i class="fas fa-envelope text-white"></i>
                </div>
                <div>
                  <p class="text-gray-300 text-sm">Email Us</p>
                  <a href="mailto:<EMAIL>" class="text-white text-lg font-semibold hover:text-yellow-400 transition-colors"><EMAIL></a>
                </div>
              </div>

              <div class="flex items-center">
                <div class="w-12 h-12 luxury-gradient rounded-xl flex items-center justify-center mr-4">
                  <i class="fas fa-map-marker-alt text-white"></i>
                </div>
                <div>
                  <p class="text-gray-300 text-sm">Visit Us</p>
                  <p class="text-white text-lg font-semibold">Pimple Nilakh, Pune</p>
                </div>
              </div>
            </div>
          </div>

          <!-- WhatsApp Contact -->
          <div class="bg-gradient-to-r from-green-500 to-green-600 rounded-2xl p-8 text-white">
            <div class="flex items-center mb-4">
              <i class="fab fa-whatsapp text-3xl mr-4"></i>
              <div>
                <h3 class="text-xl font-bold">WhatsApp Us</h3>
                <p class="text-green-100">Get instant responses</p>
              </div>
            </div>
            <p class="mb-6 text-green-100">Chat with our property experts for immediate assistance and quick answers to all your queries.</p>
            <a href="https://wa.me/917020637569?text=Hi%2C%20I%27m%20interested%20in%20Green%20Alley%20luxury%20villas.%20Please%20share%20more%20details."
               target="_blank"
               class="bg-white text-green-600 px-6 py-3 rounded-xl font-semibold hover:bg-green-50 transition-colors inline-flex items-center">
              <i class="fab fa-whatsapp mr-2"></i>
              Start Chat
            </a>
          </div>

          <!-- Office Hours -->
          <div class="bg-white bg-opacity-10 backdrop-blur-lg rounded-2xl p-8 border border-white border-opacity-20">
            <h3 class="text-2xl font-bold mb-6 text-white">Office Hours</h3>
            <div class="space-y-3 text-gray-300">
              <div class="flex justify-between">
                <span>Monday - Friday</span>
                <span class="text-white font-semibold">9:00 AM - 7:00 PM</span>
              </div>
              <div class="flex justify-between">
                <span>Saturday</span>
                <span class="text-white font-semibold">9:00 AM - 6:00 PM</span>
              </div>
              <div class="flex justify-between">
                <span>Sunday</span>
                <span class="text-white font-semibold">10:00 AM - 5:00 PM</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Background Elements -->
  <div class="absolute top-0 right-0 w-96 h-96 bg-gradient-to-bl from-yellow-400 to-transparent rounded-full opacity-10"></div>
  <div class="absolute bottom-0 left-0 w-64 h-64 bg-gradient-to-tr from-green-400 to-transparent rounded-full opacity-10"></div>
</section>

<!-- CTA Section -->
<section id="enquiry" class="py-20 px-6 md:px-16 bg-white relative overflow-hidden">
  <div class="max-w-4xl mx-auto text-center">
    <div data-aos="fade-up">
      <h2 class="text-4xl md:text-5xl font-playfair font-bold mb-6 text-gray-900">
        Ready to Experience <span class="gradient-text">Luxury Living?</span>
      </h2>
      <p class="text-xl text-gray-600 mb-8 leading-relaxed">
        Your dream villa awaits. Book your exclusive site visit today and discover the Green Alley difference.
      </p>

      <div class="flex flex-col md:flex-row justify-center items-center gap-6 mb-12">
        <a href="tel:+917507007875" class="luxury-btn">
          <i class="fas fa-phone mr-2"></i>
          Call: +91 7507007875
        </a>
        <a href="https://wa.me/917020637569?text=Hi%2C%20I%27m%20interested%20in%20booking%20a%20site%20visit%20for%20Green%20Alley%20villas"
           target="_blank"
           class="bg-green-500 hover:bg-green-600 text-white px-8 py-4 rounded-full font-semibold transition-all duration-300 hover:scale-105 hover:shadow-lg">
          <i class="fab fa-whatsapp mr-2"></i>
          WhatsApp Now
        </a>
      </div>

      <div class="grid md:grid-cols-3 gap-8 text-center">
        <div class="p-6">
          <div class="w-16 h-16 luxury-gradient rounded-full flex items-center justify-center mx-auto mb-4">
            <i class="fas fa-calendar-check text-white text-xl"></i>
          </div>
          <h3 class="text-lg font-semibold mb-2 text-gray-900">Book Site Visit</h3>
          <p class="text-gray-600">Schedule a personalized tour</p>
        </div>
        <div class="p-6">
          <div class="w-16 h-16 luxury-gradient rounded-full flex items-center justify-center mx-auto mb-4">
            <i class="fas fa-download text-white text-xl"></i>
          </div>
          <h3 class="text-lg font-semibold mb-2 text-gray-900">Download Brochure</h3>
          <p class="text-gray-600">Get detailed project information</p>
        </div>
        <div class="p-6">
          <div class="w-16 h-16 luxury-gradient rounded-full flex items-center justify-center mx-auto mb-4">
            <i class="fas fa-handshake text-white text-xl"></i>
          </div>
          <h3 class="text-lg font-semibold mb-2 text-gray-900">Expert Consultation</h3>
          <p class="text-gray-600">Speak with our property experts</p>
        </div>
      </div>
    </div>
  </div>

  <!-- Background Elements -->
  <div class="absolute top-0 left-0 w-64 h-64 bg-gradient-to-br from-yellow-200 to-yellow-400 rounded-full opacity-10 -translate-y-32 -translate-x-32"></div>
  <div class="absolute bottom-0 right-0 w-48 h-48 bg-gradient-to-tl from-green-200 to-green-400 rounded-full opacity-10 translate-y-24 translate-x-24"></div>
</section>

<!-- Footer -->
<footer class="py-16 bg-gray-900 text-white relative overflow-hidden">
  <div class="max-w-7xl mx-auto px-6 md:px-16">
    <div class="grid md:grid-cols-4 gap-8 mb-12">
      <!-- Company Info -->
      <div class="md:col-span-2">
        <div class="text-3xl font-playfair font-bold mb-4">
          <span class="gradient-text">Green Alley</span>
        </div>
        <p class="text-gray-300 mb-6 leading-relaxed">
          Experience luxury living in the heart of Pune with our premium 4 BHK villas featuring contemporary design, green views, and world-class amenities.
        </p>
        <div class="flex space-x-4">
          <a href="#" class="w-10 h-10 bg-white bg-opacity-10 rounded-full flex items-center justify-center hover:bg-opacity-20 transition-all duration-300">
            <i class="fab fa-facebook-f text-white"></i>
          </a>
          <a href="#" class="w-10 h-10 bg-white bg-opacity-10 rounded-full flex items-center justify-center hover:bg-opacity-20 transition-all duration-300">
            <i class="fab fa-instagram text-white"></i>
          </a>
          <a href="#" class="w-10 h-10 bg-white bg-opacity-10 rounded-full flex items-center justify-center hover:bg-opacity-20 transition-all duration-300">
            <i class="fab fa-youtube text-white"></i>
          </a>
          <a href="#" class="w-10 h-10 bg-white bg-opacity-10 rounded-full flex items-center justify-center hover:bg-opacity-20 transition-all duration-300">
            <i class="fab fa-linkedin-in text-white"></i>
          </a>
        </div>
      </div>

      <!-- Quick Links -->
      <div>
        <h3 class="text-lg font-semibold mb-4 text-white">Quick Links</h3>
        <ul class="space-y-2">
          <li><a href="#home" class="text-gray-300 hover:text-yellow-400 transition-colors">Home</a></li>
          <li><a href="#about" class="text-gray-300 hover:text-yellow-400 transition-colors">About</a></li>
          <li><a href="#gallery" class="text-gray-300 hover:text-yellow-400 transition-colors">Gallery</a></li>
          <li><a href="#amenities" class="text-gray-300 hover:text-yellow-400 transition-colors">Amenities</a></li>
          <li><a href="#location" class="text-gray-300 hover:text-yellow-400 transition-colors">Location</a></li>
          <li><a href="#contact" class="text-gray-300 hover:text-yellow-400 transition-colors">Contact</a></li>
        </ul>
      </div>

      <!-- Contact Info -->
      <div>
        <h3 class="text-lg font-semibold mb-4 text-white">Contact Info</h3>
        <div class="space-y-3">
          <div class="flex items-center">
            <i class="fas fa-phone text-yellow-400 mr-3"></i>
            <a href="tel:+917507007875" class="text-gray-300 hover:text-white transition-colors">+91 7507007875</a>
          </div>
          <div class="flex items-center">
            <i class="fas fa-envelope text-yellow-400 mr-3"></i>
            <a href="mailto:<EMAIL>" class="text-gray-300 hover:text-white transition-colors"><EMAIL></a>
          </div>
          <div class="flex items-start">
            <i class="fas fa-map-marker-alt text-yellow-400 mr-3 mt-1"></i>
            <span class="text-gray-300">Pimple Nilakh, Pune, Maharashtra</span>
          </div>
        </div>
      </div>
    </div>

    <div class="border-t border-gray-700 pt-8 text-center">
      <p class="text-gray-400">© 2025 Green Alley. All rights reserved. | Developed by TechBurst Solutions</p>
    </div>
  </div>

  <!-- Background Elements -->
  <div class="absolute top-0 right-0 w-64 h-64 bg-gradient-to-bl from-yellow-400 to-transparent rounded-full opacity-5"></div>
  <div class="absolute bottom-0 left-0 w-48 h-48 bg-gradient-to-tr from-green-400 to-transparent rounded-full opacity-5"></div>
</footer>

<!-- Floating WhatsApp Button -->
<div class="fixed bottom-30 right-30 z-1000">
  <!-- Tooltip -->
  <div class="absolute bottom-full right-0 mb-2 px-3 py-2 bg-black text-white text-sm rounded-lg opacity-0 transform translate-y-2 transition-all duration-300 whitespace-nowrap whatsapp-tooltip">
    Chat with us on WhatsApp
    <div class="absolute top-full right-4 w-0 h-0 border-l-4 border-r-4 border-t-4 border-transparent border-t-black"></div>
  </div>

  <!-- Notification Badge -->
  <div class="absolute -top-1 -right-1 w-4 h-4 bg-red-500 rounded-full flex items-center justify-center">
    <span class="text-xs text-white font-bold">1</span>
  </div>

  <a href="https://wa.me/917020637569?text=Hi%2C%20I%27m%20interested%20in%20Green%20Alley%20luxury%20villas.%20Please%20share%20more%20details."
     target="_blank"
     class="floating-whatsapp magnetic"
     onmouseenter="showWhatsAppTooltip()"
     onmouseleave="hideWhatsAppTooltip()">
    <i class="fab fa-whatsapp"></i>
  </a>
</div>

<style>
.whatsapp-tooltip.show {
  opacity: 1;
  transform: translateY(0);
}
</style>

<script>
function showWhatsAppTooltip() {
  document.querySelector('.whatsapp-tooltip').classList.add('show');
}

function hideWhatsAppTooltip() {
  document.querySelector('.whatsapp-tooltip').classList.remove('show');
}

// Auto-hide notification after 5 seconds
setTimeout(() => {
  const badge = document.querySelector('.floating-whatsapp').parentElement.querySelector('.bg-red-500');
  if (badge) {
    badge.style.opacity = '0';
    badge.style.transform = 'scale(0)';
  }
}, 5000);
</script>

<!-- JavaScript Libraries -->
<script src="https://unpkg.com/swiper@8/swiper-bundle.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/aos/2.3.4/aos.js"></script>

<script>
// Initialize AOS (Animate On Scroll)
AOS.init({
  duration: 1000,
  easing: 'ease-in-out-cubic',
  once: true,
  offset: 100,
  delay: 100
});

// Initialize Swiper for Gallery
const gallerySwiper = new Swiper('.gallery-main', {
  loop: true,
  autoplay: {
    delay: 5000,
    disableOnInteraction: false,
  },
  pagination: {
    el: '.swiper-pagination',
    clickable: true,
  },
  navigation: {
    nextEl: '.swiper-button-next',
    prevEl: '.swiper-button-prev',
  },
  effect: 'fade',
  fadeEffect: {
    crossFade: true
  },
  speed: 1000
});

// Enhanced Mobile Menu Toggle
const mobileMenuBtn = document.getElementById('mobile-menu-btn');
const mobileMenu = document.getElementById('mobile-menu');
const hamburgerMenu = mobileMenuBtn.querySelector('.hamburger-menu');

mobileMenuBtn.addEventListener('click', () => {
  mobileMenu.classList.toggle('hidden');
  mobileMenu.classList.toggle('translate-y-full');
  hamburgerMenu.classList.toggle('active');
});

// Enhanced Navbar Scroll Effect
window.addEventListener('scroll', () => {
  const navbar = document.getElementById('navbar');
  const scrollY = window.scrollY;

  if (scrollY > 100) {
    navbar.classList.add('bg-opacity-95', 'backdrop-blur-xl');
    navbar.style.transform = 'translateY(0)';
  } else {
    navbar.classList.remove('bg-opacity-95', 'backdrop-blur-xl');
  }

  // Hide navbar on scroll down, show on scroll up
  if (scrollY > 200) {
    if (scrollY > window.lastScrollY) {
      navbar.style.transform = 'translateY(-100%)';
    } else {
      navbar.style.transform = 'translateY(0)';
    }
  }
  window.lastScrollY = scrollY;
});

// Smooth Scrolling for Navigation Links
document.querySelectorAll('a[href^="#"]').forEach(anchor => {
  anchor.addEventListener('click', function (e) {
    e.preventDefault();
    const target = document.querySelector(this.getAttribute('href'));
    if (target) {
      target.scrollIntoView({
        behavior: 'smooth',
        block: 'start'
      });
      // Close mobile menu if open
      mobileMenu.classList.add('hidden');
      mobileMenu.classList.add('translate-y-full');
      hamburgerMenu.classList.remove('active');
    }
  });
});

// Counter Animation
function animateCounters() {
  const counters = document.querySelectorAll('.counter');
  counters.forEach(counter => {
    const target = parseInt(counter.getAttribute('data-target'));
    const increment = target / 100;
    let current = 0;

    const updateCounter = () => {
      if (current < target) {
        current += increment;
        counter.textContent = Math.ceil(current);
        requestAnimationFrame(updateCounter);
      } else {
        counter.textContent = target;
      }
    };

    updateCounter();
  });
}

// Progress Bar Animation
function animateProgressBars() {
  const progressBars = document.querySelectorAll('.progress-bar');
  progressBars.forEach(bar => {
    const width = bar.getAttribute('data-width');
    setTimeout(() => {
      bar.style.width = width + '%';
    }, 500);
  });
}

// Intersection Observer for animations
const observerOptions = {
  threshold: 0.3,
  rootMargin: '0px 0px -50px 0px'
};

const observer = new IntersectionObserver((entries) => {
  entries.forEach(entry => {
    if (entry.isIntersecting) {
      // Animate counters
      if (entry.target.querySelector('.counter')) {
        animateCounters();
      }

      // Animate progress bars
      if (entry.target.querySelector('.progress-bar')) {
        animateProgressBars();
      }

      // Add fade-in-up animation
      entry.target.classList.add('fade-in-up');
    }
  });
}, observerOptions);

// Observe sections for animation
document.querySelectorAll('section').forEach(section => {
  observer.observe(section);
});

// Magnetic Effect for buttons and interactive elements
document.querySelectorAll('.magnetic').forEach(element => {
  element.addEventListener('mousemove', (e) => {
    const rect = element.getBoundingClientRect();
    const x = e.clientX - rect.left - rect.width / 2;
    const y = e.clientY - rect.top - rect.height / 2;

    element.style.transform = `translate(${x * 0.1}px, ${y * 0.1}px) scale(1.05)`;
  });

  element.addEventListener('mouseleave', () => {
    element.style.transform = 'translate(0px, 0px) scale(1)';
  });
});

// Parallax Effect for Hero Section
window.addEventListener('scroll', () => {
  const scrolled = window.pageYOffset;
  const parallaxElements = document.querySelectorAll('.parallax');

  parallaxElements.forEach(element => {
    const speed = element.dataset.speed || 0.5;
    const yPos = -(scrolled * speed);
    element.style.transform = `translateY(${yPos}px)`;
  });
});

// Tilt Effect
document.querySelectorAll('.tilt-effect').forEach(element => {
  element.addEventListener('mousemove', (e) => {
    const rect = element.getBoundingClientRect();
    const x = e.clientX - rect.left;
    const y = e.clientY - rect.top;

    const centerX = rect.width / 2;
    const centerY = rect.height / 2;

    const rotateX = (y - centerY) / 10;
    const rotateY = (centerX - x) / 10;

    element.style.transform = `perspective(1000px) rotateX(${rotateX}deg) rotateY(${rotateY}deg) scale3d(1.05, 1.05, 1.05)`;
  });

  element.addEventListener('mouseleave', () => {
    element.style.transform = 'perspective(1000px) rotateX(0deg) rotateY(0deg) scale3d(1, 1, 1)';
  });
});

// Cursor Trail Effect
let mouseX = 0, mouseY = 0;
let trailX = 0, trailY = 0;

document.addEventListener('mousemove', (e) => {
  mouseX = e.clientX;
  mouseY = e.clientY;
});

function animateTrail() {
  trailX += (mouseX - trailX) * 0.1;
  trailY += (mouseY - trailY) * 0.1;

  // Create trail particles
  if (Math.random() > 0.9) {
    createTrailParticle(trailX, trailY);
  }

  requestAnimationFrame(animateTrail);
}

function createTrailParticle(x, y) {
  const particle = document.createElement('div');
  particle.className = 'fixed w-1 h-1 bg-yellow-400 rounded-full pointer-events-none z-50 opacity-70';
  particle.style.left = x + 'px';
  particle.style.top = y + 'px';

  document.body.appendChild(particle);

  setTimeout(() => {
    particle.style.opacity = '0';
    particle.style.transform = 'scale(0)';
    setTimeout(() => particle.remove(), 300);
  }, 100);
}

animateTrail();

// Custom Cursor Functionality
const customCursor = document.getElementById('customCursor');
const customCursorDot = document.getElementById('customCursorDot');
let cursorTrails = [];

// Update cursor position
document.addEventListener('mousemove', (e) => {
  const x = e.clientX;
  const y = e.clientY;

  // Update main cursor
  customCursor.style.left = (x - 10) + 'px';
  customCursor.style.top = (y - 10) + 'px';

  // Update cursor dot
  customCursorDot.style.left = (x - 2) + 'px';
  customCursorDot.style.top = (y - 2) + 'px';

  // Create cursor trail
  createCursorTrail(x, y);
});

// Create cursor trail effect
function createCursorTrail(x, y) {
  if (Math.random() > 0.7) { // Reduce frequency for performance
    const trail = document.createElement('div');
    trail.className = 'cursor-trail';
    trail.style.left = (x - 3) + 'px';
    trail.style.top = (y - 3) + 'px';

    document.body.appendChild(trail);

    // Animate trail
    setTimeout(() => {
      trail.style.opacity = '0';
      trail.style.transform = 'scale(0)';
    }, 50);

    // Remove trail element
    setTimeout(() => {
      if (trail.parentNode) {
        trail.parentNode.removeChild(trail);
      }
    }, 350);
  }
}

// Cursor hover effects for interactive elements
const interactiveElements = document.querySelectorAll('a, button, .luxury-btn, .magnetic, .hover-lift, .nav-link');

interactiveElements.forEach(element => {
  element.addEventListener('mouseenter', () => {
    customCursor.classList.add('cursor-hover', 'cursor-glow');
    customCursor.style.mixBlendMode = 'normal';

    // Special effects for specific elements
    if (element.closest('#gallery')) {
      customCursor.classList.add('cursor-gallery');
    } else if (element.closest('#contact') || element.href && element.href.includes('mailto')) {
      customCursor.classList.add('cursor-contact');
    } else if (element.href && element.href.includes('whatsapp')) {
      customCursor.classList.add('cursor-whatsapp');
    }
  });

  element.addEventListener('mouseleave', () => {
    customCursor.classList.remove('cursor-hover', 'cursor-glow', 'cursor-gallery', 'cursor-contact', 'cursor-whatsapp');
    customCursor.style.mixBlendMode = 'difference';
  });
});

// Special cursor effects for gallery images
const galleryImages = document.querySelectorAll('#gallery img');
galleryImages.forEach(img => {
  img.addEventListener('mouseenter', () => {
    customCursor.innerHTML = '<i class="fas fa-search-plus text-white text-xs"></i>';
    customCursor.style.display = 'flex';
    customCursor.style.alignItems = 'center';
    customCursor.style.justifyContent = 'center';
  });

  img.addEventListener('mouseleave', () => {
    customCursor.innerHTML = '';
  });
});

// Cursor effect for form elements
const formElements = document.querySelectorAll('input, textarea, select');
formElements.forEach(element => {
  element.addEventListener('focus', () => {
    customCursor.style.background = 'linear-gradient(45deg, #D4AF37, #FFD700)';
    customCursor.style.borderRadius = '2px';
    customCursor.style.width = '2px';
    customCursor.style.height = '20px';
  });

  element.addEventListener('blur', () => {
    customCursor.style.background = 'radial-gradient(circle, rgba(212, 175, 55, 0.8) 0%, rgba(255, 215, 0, 0.6) 50%, transparent 70%)';
    customCursor.style.borderRadius = '50%';
    customCursor.style.width = '20px';
    customCursor.style.height = '20px';
  });
});

// Cursor click effect with ripple
document.addEventListener('mousedown', (e) => {
  customCursor.classList.add('cursor-click');
  customCursorDot.style.transform = 'scale(1.5)';

  // Create click ripple effect
  createClickRipple(e.clientX, e.clientY);
});

document.addEventListener('mouseup', () => {
  customCursor.classList.remove('cursor-click');
  customCursorDot.style.transform = 'scale(1)';
});

// Create click ripple effect
function createClickRipple(x, y) {
  const ripple = document.createElement('div');
  ripple.style.position = 'fixed';
  ripple.style.left = (x - 15) + 'px';
  ripple.style.top = (y - 15) + 'px';
  ripple.style.width = '30px';
  ripple.style.height = '30px';
  ripple.style.borderRadius = '50%';
  ripple.style.background = 'radial-gradient(circle, rgba(212, 175, 55, 0.6) 0%, transparent 70%)';
  ripple.style.border = '2px solid rgba(212, 175, 55, 0.8)';
  ripple.style.pointerEvents = 'none';
  ripple.style.zIndex = '9999';
  ripple.style.transform = 'scale(0)';
  ripple.style.transition = 'transform 0.6s ease-out, opacity 0.6s ease-out';
  ripple.style.opacity = '1';

  document.body.appendChild(ripple);

  // Animate ripple
  requestAnimationFrame(() => {
    ripple.style.transform = 'scale(3)';
    ripple.style.opacity = '0';
  });

  // Remove ripple
  setTimeout(() => {
    if (ripple.parentNode) {
      ripple.parentNode.removeChild(ripple);
    }
  }, 600);
}

// Text cursor for input fields
const textInputs = document.querySelectorAll('input[type="text"], input[type="email"], input[type="tel"], textarea, select');

textInputs.forEach(input => {
  input.addEventListener('mouseenter', () => {
    customCursor.classList.add('cursor-text');
    customCursor.style.width = '2px';
    customCursor.style.height = '20px';
    customCursor.style.borderRadius = '1px';
    customCursor.style.background = 'linear-gradient(180deg, #D4AF37 0%, #FFD700 100%)';
  });

  input.addEventListener('mouseleave', () => {
    customCursor.classList.remove('cursor-text');
    customCursor.style.width = '20px';
    customCursor.style.height = '20px';
    customCursor.style.borderRadius = '50%';
    customCursor.style.background = 'radial-gradient(circle, rgba(212, 175, 55, 0.8) 0%, rgba(255, 215, 0, 0.6) 50%, transparent 70%)';
  });
});

// Hide cursor when leaving window
document.addEventListener('mouseleave', () => {
  customCursor.style.opacity = '0';
  customCursorDot.style.opacity = '0';
});

document.addEventListener('mouseenter', () => {
  customCursor.style.opacity = '1';
  customCursorDot.style.opacity = '1';
});

// Disable custom cursor on touch devices
if ('ontouchstart' in window || navigator.maxTouchPoints > 0) {
  customCursor.style.display = 'none';
  customCursorDot.style.display = 'none';
  document.body.style.cursor = 'auto';
  document.querySelectorAll('*').forEach(el => {
    el.style.cursor = 'auto';
  });
}

// Enhanced scrollbar interaction and scroll progress
let isScrolling = false;
const scrollProgress = document.getElementById('scrollProgress');

window.addEventListener('scroll', () => {
  // Update scroll progress
  const scrollTop = window.pageYOffset;
  const docHeight = document.body.scrollHeight - window.innerHeight;
  const scrollPercent = (scrollTop / docHeight) * 100;

  scrollProgress.style.width = scrollPercent + '%';

  // Cursor effect during scroll
  if (!isScrolling) {
    isScrolling = true;
    customCursor.style.background = 'radial-gradient(circle, rgba(255, 215, 0, 1) 0%, rgba(212, 175, 55, 0.8) 50%, transparent 70%)';

    setTimeout(() => {
      isScrolling = false;
      customCursor.style.background = 'radial-gradient(circle, rgba(212, 175, 55, 0.8) 0%, rgba(255, 215, 0, 0.6) 50%, transparent 70%)';
    }, 150);
  }

  // Dynamic scroll progress color based on scroll position
  if (scrollPercent < 25) {
    scrollProgress.style.background = 'linear-gradient(to right, #D4AF37, #FFD700)';
  } else if (scrollPercent < 50) {
    scrollProgress.style.background = 'linear-gradient(to right, #FFD700, #D4AF37, #FFD700)';
  } else if (scrollPercent < 75) {
    scrollProgress.style.background = 'linear-gradient(to right, #D4AF37, #FFD700, #B8860B)';
  } else {
    scrollProgress.style.background = 'linear-gradient(to right, #FFD700, #D4AF37, #B8860B, #FFD700)';
  }
});

// Contact Form Handling
const contactForm = document.getElementById('contactForm');
contactForm.addEventListener('submit', function(e) {
  e.preventDefault();

  // Get form data
  const formData = new FormData(contactForm);
  const firstName = formData.get('firstName');
  const lastName = formData.get('lastName');
  const email = formData.get('email');
  const phone = formData.get('phone');
  const interest = formData.get('interest');
  const message = formData.get('message');

  // Create mailto link
  const subject = `Green Alley Inquiry from ${firstName} ${lastName}`;
  const body = `
Name: ${firstName} ${lastName}
Email: ${email}
Phone: ${phone}
Interest: ${interest}
Message: ${message}

Best regards,
${firstName} ${lastName}
  `;

  const mailtoLink = `mailto:<EMAIL>?subject=${encodeURIComponent(subject)}&body=${encodeURIComponent(body)}`;

  // Open email client
  window.location.href = mailtoLink;

  // Show success message
  alert('Thank you for your inquiry! Your email client will open with a pre-filled message. Please send it to complete your inquiry.');

  // Reset form
  contactForm.reset();
});

// Parallax Effect for Hero Section
window.addEventListener('scroll', () => {
  const scrolled = window.pageYOffset;
  const parallaxElements = document.querySelectorAll('.parallax');

  parallaxElements.forEach(element => {
    const speed = element.dataset.speed || 0.5;
    const yPos = -(scrolled * speed);
    element.style.transform = `translateY(${yPos}px)`;
  });
});

// Add loading animation
window.addEventListener('load', () => {
  document.body.classList.add('loaded');
});

// Observe hover-lift elements for animation
document.querySelectorAll('.hover-lift').forEach(el => {
  observer.observe(el);
});
</script>

</body>
</html>
